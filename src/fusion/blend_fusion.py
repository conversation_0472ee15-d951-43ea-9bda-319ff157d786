"""
混合融合模块
Blend Fusion Module
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from enum import Enum

from ..utils.logger import Logger
from ..video.video_loader import VideoLoader


class BlendMode(Enum):
    """混合模式枚举"""
    LINEAR = "linear"              # 线性混合
    WEIGHTED = "weighted"          # 加权混合
    ALPHA_BLEND = "alpha_blend"    # Alpha混合
    FEATHER = "feather"            # 羽化混合
    GRADIENT = "gradient"          # 渐变混合


class BlendRegion:
    """混合区域类"""

    def __init__(self, x: int = 0, y: int = 0, width: int = 0, height: int = 0):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.mask: Optional[np.ndarray] = None
        self.feather_radius = 0

    def set_mask(self, mask: np.ndarray):
        """设置自定义掩码"""
        self.mask = mask

    def set_feather(self, radius: int):
        """设置羽化半径"""
        self.feather_radius = radius

    def create_gradient_mask(self, shape: Tuple[int, int], direction: str = "horizontal") -> np.ndarray:
        """创建渐变掩码"""
        h, w = shape
        mask = np.zeros((h, w), dtype=np.float32)

        if direction == "horizontal":
            for x in range(w):
                mask[:, x] = x / (w - 1) if w > 1 else 0
        elif direction == "vertical":
            for y in range(h):
                mask[y, :] = y / (h - 1) if h > 1 else 0
        elif direction == "radial":
            center_x, center_y = w // 2, h // 2
            max_dist = np.sqrt(center_x**2 + center_y**2)
            for y in range(h):
                for x in range(w):
                    dist = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                    mask[y, x] = min(dist / max_dist, 1.0) if max_dist > 0 else 0

        return mask


class BlendFusion:
    """混合融合类"""

    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.video_a_loader: Optional[VideoLoader] = None
        self.video_b_loader: Optional[VideoLoader] = None
        self.result_frames: List[np.ndarray] = []
        self.logger.info("混合融合模块初始化完成")

    def set_videos(self, video_a_loader: VideoLoader, video_b_loader: VideoLoader):
        """设置要融合的视频"""
        if not video_a_loader.is_loaded():
            raise ValueError("视频A未正确加载")
        if not video_b_loader.is_loaded():
            raise ValueError("视频B未正确加载")

        self.video_a_loader = video_a_loader
        self.video_b_loader = video_b_loader

        self.logger.info("混合融合视频设置完成")

    def blend_fusion(self, alpha: float = 0.5,
                    mode: BlendMode = BlendMode.LINEAR,
                    region: Optional[BlendRegion] = None,
                    frame_range: Optional[Tuple[int, int]] = None,
                    resize_mode: str = "fit") -> List[np.ndarray]:
        """执行混合融合

        Args:
            alpha: 混合权重 (0.0-1.0)
            mode: 混合模式
            region: 混合区域
            frame_range: 融合帧范围 (start_frame, end_frame)
            resize_mode: A视频调整模式

        Returns:
            融合后的帧序列
        """
        try:
            if not self.video_a_loader or not self.video_b_loader:
                raise ValueError("视频未设置")

            self.logger.info(f"开始混合融合，模式: {mode.value}, 权重: {alpha}")

            # 获取B视频的所有帧
            b_frames = []
            for frame_number, frame in self.video_b_loader.get_frame_iterator():
                b_frames.append(frame.copy())

            # 获取A视频的帧
            a_frames = []
            for frame_number, frame in self.video_a_loader.get_frame_iterator():
                a_frames.append(frame.copy())

            if not a_frames or not b_frames:
                raise ValueError("无法获取视频帧")

            # 确定融合范围
            if frame_range:
                start_frame, end_frame = frame_range
                start_frame = max(0, start_frame)
                end_frame = min(len(b_frames), end_frame)
            else:
                start_frame, end_frame = 0, len(b_frames)

            result_frames = []

            for i, b_frame in enumerate(b_frames):
                if start_frame <= i < end_frame:
                    # 在融合范围内，执行混合
                    fusion_index = i - start_frame
                    a_frame_index = min(fusion_index % len(a_frames), len(a_frames) - 1)
                    a_frame = a_frames[a_frame_index]

                    # 调整A帧尺寸以匹配B帧
                    a_frame_resized = self._resize_frame(a_frame, b_frame.shape[:2], resize_mode)

                    # 执行混合
                    blended_frame = self._blend_frames(
                        b_frame, a_frame_resized, alpha, mode, region, fusion_index,
                        end_frame - start_frame
                    )
                    result_frames.append(blended_frame)
                else:
                    # 不在融合范围内，直接使用B帧
                    result_frames.append(b_frame.copy())

            self.result_frames = result_frames
            self.logger.info(f"混合融合完成，结果帧数: {len(result_frames)}")
            return result_frames

        except Exception as e:
            self.logger.error(f"混合融合失败: {e}")
            raise

    def _resize_frame(self, frame: np.ndarray, target_shape: Tuple[int, int], mode: str) -> np.ndarray:
        """调整帧尺寸"""
        try:
            target_h, target_w = target_shape

            if mode == "fit":
                # 保持宽高比适应
                h, w = frame.shape[:2]
                scale = min(target_w / w, target_h / h)
                new_w = int(w * scale)
                new_h = int(h * scale)

                resized = cv2.resize(frame, (new_w, new_h))

                # 创建目标大小的画布
                result = np.zeros((target_h, target_w, frame.shape[2]), dtype=frame.dtype)

                # 居中放置
                y_offset = (target_h - new_h) // 2
                x_offset = (target_w - new_w) // 2
                result[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized

                return result
            else:
                # 直接拉伸
                return cv2.resize(frame, (target_w, target_h))

        except Exception as e:
            self.logger.error(f"调整帧尺寸失败: {e}")
            return frame

    def _blend_frames(self, background: np.ndarray,
                     foreground: np.ndarray,
                     alpha: float,
                     mode: BlendMode,
                     region: Optional[BlendRegion],
                     frame_index: int,
                     total_frames: int) -> np.ndarray:
        """混合两个帧"""
        try:
            result = background.copy().astype(np.float32)
            fg = foreground.astype(np.float32)

            # 创建混合掩码
            if region:
                mask = self._create_blend_mask(background.shape[:2], region, frame_index, total_frames)
            else:
                mask = np.ones(background.shape[:2], dtype=np.float32)

            # 应用混合模式
            if mode == BlendMode.LINEAR:
                # 线性混合
                blended = result * (1 - alpha) + fg * alpha

            elif mode == BlendMode.WEIGHTED:
                # 加权混合（使用掩码权重）
                weight = mask * alpha
                blended = result * (1 - weight[:, :, np.newaxis]) + fg * weight[:, :, np.newaxis]

            elif mode == BlendMode.ALPHA_BLEND:
                # Alpha混合
                alpha_mask = mask * alpha
                blended = result * (1 - alpha_mask[:, :, np.newaxis]) + fg * alpha_mask[:, :, np.newaxis]

            elif mode == BlendMode.FEATHER:
                # 羽化混合
                feathered_mask = self._apply_feather(mask, region.feather_radius if region else 5)
                weight = feathered_mask * alpha
                blended = result * (1 - weight[:, :, np.newaxis]) + fg * weight[:, :, np.newaxis]

            elif mode == BlendMode.GRADIENT:
                # 渐变混合
                gradient_alpha = self._calculate_gradient_alpha(alpha, frame_index, total_frames)
                weight = mask * gradient_alpha
                blended = result * (1 - weight[:, :, np.newaxis]) + fg * weight[:, :, np.newaxis]

            else:
                blended = result * (1 - alpha) + fg * alpha

            # 限制到有效范围并转换回uint8
            blended = np.clip(blended, 0, 255).astype(np.uint8)
            return blended

        except Exception as e:
            self.logger.error(f"混合帧失败: {e}")
            return background.copy()

    def _create_blend_mask(self, shape: Tuple[int, int],
                          region: BlendRegion,
                          frame_index: int,
                          total_frames: int) -> np.ndarray:
        """创建混合掩码"""
        try:
            h, w = shape

            if region.mask is not None:
                # 使用自定义掩码
                mask = cv2.resize(region.mask.astype(np.float32), (w, h))
                return mask / 255.0 if mask.max() > 1.0 else mask

            # 创建矩形区域掩码
            mask = np.zeros((h, w), dtype=np.float32)

            x1 = max(0, region.x)
            y1 = max(0, region.y)
            x2 = min(w, region.x + region.width)
            y2 = min(h, region.y + region.height)

            if x2 > x1 and y2 > y1:
                mask[y1:y2, x1:x2] = 1.0

            return mask

        except Exception as e:
            self.logger.error(f"创建混合掩码失败: {e}")
            return np.ones(shape, dtype=np.float32)

    def _apply_feather(self, mask: np.ndarray, radius: int) -> np.ndarray:
        """应用羽化效果"""
        try:
            if radius <= 0:
                return mask

            # 使用高斯模糊实现羽化
            kernel_size = radius * 2 + 1
            feathered = cv2.GaussianBlur(mask, (kernel_size, kernel_size), radius / 3.0)

            return feathered

        except Exception as e:
            self.logger.error(f"应用羽化失败: {e}")
            return mask

    def _calculate_gradient_alpha(self, base_alpha: float, frame_index: int, total_frames: int) -> float:
        """计算渐变透明度"""
        try:
            if total_frames <= 1:
                return base_alpha

            # 创建渐变效果（从0到base_alpha再到0）
            progress = frame_index / (total_frames - 1)

            if progress <= 0.5:
                # 前半段：从0渐变到base_alpha
                gradient_factor = progress * 2
            else:
                # 后半段：从base_alpha渐变到0
                gradient_factor = (1 - progress) * 2

            return base_alpha * gradient_factor

        except Exception as e:
            self.logger.error(f"计算渐变透明度失败: {e}")
            return base_alpha

    def create_region_mask(self, shape: Tuple[int, int],
                          x: int, y: int, width: int, height: int,
                          feather_radius: int = 0) -> BlendRegion:
        """创建区域掩码"""
        region = BlendRegion(x, y, width, height)
        region.set_feather(feather_radius)
        return region

    def create_gradient_region(self, shape: Tuple[int, int],
                             direction: str = "horizontal") -> BlendRegion:
        """创建渐变区域"""
        region = BlendRegion(0, 0, shape[1], shape[0])
        gradient_mask = region.create_gradient_mask(shape, direction)
        region.set_mask((gradient_mask * 255).astype(np.uint8))
        return region

    def get_result_frames(self) -> List[np.ndarray]:
        """获取融合结果帧"""
        return self.result_frames.copy()

    def save_result_video(self, output_path: str, fps: float = 30.0,
                         codec: str = 'mp4v') -> bool:
        """保存融合结果为视频文件"""
        try:
            if not self.result_frames:
                self.logger.error("没有可保存的结果帧")
                return False

            # 获取第一帧的尺寸
            first_frame = self.result_frames[0]
            h, w = first_frame.shape[:2]

            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*codec)
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (w, h))

            if not video_writer.isOpened():
                self.logger.error(f"无法创建视频写入器: {output_path}")
                return False

            # 写入所有帧
            for i, frame in enumerate(self.result_frames):
                video_writer.write(frame)

                if (i + 1) % 100 == 0:
                    self.logger.info(f"已写入 {i + 1}/{len(self.result_frames)} 帧")

            video_writer.release()
            self.logger.info(f"混合融合视频保存完成: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"保存混合融合视频失败: {e}")
            return False
