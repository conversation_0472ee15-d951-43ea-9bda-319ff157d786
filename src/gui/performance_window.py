"""
性能监控窗口类
Performance Monitor Window Class
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, 
                             QTextEdit, QPushButton, QProgressBar, QLabel,
                             QTabWidget, QWidget, QTableWidget, QTableWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
import psutil
import time
from datetime import datetime


class PerformanceWindow(QDialog):
    """性能监控窗口"""
    
    # 信号定义
    performance_exported = pyqtSignal(str)  # 导出路径
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("性能监控")
        self.setModal(False)  # 非模态窗口
        self.resize(900, 700)
        
        self.performance_data = []  # 性能数据历史
        self.max_data_points = 100  # 最大数据点数
        
        self.init_ui()
        self.setup_monitoring()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 实时监控标签页
        realtime_tab = self.create_realtime_tab()
        tab_widget.addTab(realtime_tab, "实时监控")
        
        # 历史数据标签页
        history_tab = self.create_history_tab()
        tab_widget.addTab(history_tab, "历史数据")
        
        # 系统信息标签页
        system_tab = self.create_system_tab()
        tab_widget.addTab(system_tab, "系统信息")
        
        layout.addWidget(tab_widget)
        
        # 按钮组
        btn_layout = QHBoxLayout()
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_performance)
        btn_layout.addWidget(refresh_btn)
        
        # 清除历史按钮
        clear_btn = QPushButton("清除历史")
        clear_btn.clicked.connect(self.clear_history)
        btn_layout.addWidget(clear_btn)
        
        # 导出报告按钮
        export_btn = QPushButton("导出报告")
        export_btn.clicked.connect(self.export_report)
        btn_layout.addWidget(export_btn)
        
        btn_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        btn_layout.addWidget(close_btn)
        
        layout.addLayout(btn_layout)
        
    def create_realtime_tab(self):
        """创建实时监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # CPU使用率
        cpu_group = QGroupBox("CPU使用率")
        cpu_layout = QVBoxLayout(cpu_group)
        
        self.cpu_label = QLabel("CPU: 0%")
        cpu_layout.addWidget(self.cpu_label)
        
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setRange(0, 100)
        cpu_layout.addWidget(self.cpu_progress)
        
        layout.addWidget(cpu_group)
        
        # 内存使用率
        memory_group = QGroupBox("内存使用率")
        memory_layout = QVBoxLayout(memory_group)
        
        self.memory_label = QLabel("内存: 0%")
        memory_layout.addWidget(self.memory_label)
        
        self.memory_progress = QProgressBar()
        self.memory_progress.setRange(0, 100)
        memory_layout.addWidget(self.memory_progress)
        
        layout.addWidget(memory_group)
        
        # 磁盘I/O
        disk_group = QGroupBox("磁盘I/O")
        disk_layout = QVBoxLayout(disk_group)
        
        self.disk_read_label = QLabel("读取: 0 MB/s")
        disk_layout.addWidget(self.disk_read_label)
        
        self.disk_write_label = QLabel("写入: 0 MB/s")
        disk_layout.addWidget(self.disk_write_label)
        
        layout.addWidget(disk_group)
        
        # 网络I/O
        network_group = QGroupBox("网络I/O")
        network_layout = QVBoxLayout(network_group)
        
        self.network_sent_label = QLabel("发送: 0 MB/s")
        network_layout.addWidget(self.network_sent_label)
        
        self.network_recv_label = QLabel("接收: 0 MB/s")
        network_layout.addWidget(self.network_recv_label)
        
        layout.addWidget(network_group)
        
        return widget
        
    def create_history_tab(self):
        """创建历史数据标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 历史数据表格
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(6)
        self.history_table.setHorizontalHeaderLabels([
            "时间", "CPU(%)", "内存(%)", "磁盘读(MB/s)", "磁盘写(MB/s)", "网络(MB/s)"
        ])
        
        layout.addWidget(self.history_table)
        
        return widget
        
    def create_system_tab(self):
        """创建系统信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        self.system_text = QTextEdit()
        self.system_text.setReadOnly(True)
        self.system_text.setFont(QFont("Consolas", 9))
        
        layout.addWidget(self.system_text)
        
        # 更新系统信息
        self.update_system_info()
        
        return widget
        
    def setup_monitoring(self):
        """设置性能监控"""
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_performance)
        self.monitor_timer.start(2000)  # 每2秒更新一次
        
        # 初始化基准值
        self.last_disk_io = psutil.disk_io_counters()
        self.last_network_io = psutil.net_io_counters()
        self.last_time = time.time()
        
    def update_performance(self):
        """更新性能数据"""
        try:
            current_time = time.time()
            time_delta = current_time - self.last_time
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent()
            self.cpu_label.setText(f"CPU: {cpu_percent:.1f}%")
            self.cpu_progress.setValue(int(cpu_percent))
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.memory_label.setText(f"内存: {memory_percent:.1f}% ({memory.used // 1024 // 1024} MB / {memory.total // 1024 // 1024} MB)")
            self.memory_progress.setValue(int(memory_percent))
            
            # 磁盘I/O
            current_disk_io = psutil.disk_io_counters()
            if self.last_disk_io and time_delta > 0:
                disk_read_speed = (current_disk_io.read_bytes - self.last_disk_io.read_bytes) / time_delta / 1024 / 1024
                disk_write_speed = (current_disk_io.write_bytes - self.last_disk_io.write_bytes) / time_delta / 1024 / 1024
                
                self.disk_read_label.setText(f"读取: {disk_read_speed:.2f} MB/s")
                self.disk_write_label.setText(f"写入: {disk_write_speed:.2f} MB/s")
            
            # 网络I/O
            current_network_io = psutil.net_io_counters()
            if self.last_network_io and time_delta > 0:
                network_sent_speed = (current_network_io.bytes_sent - self.last_network_io.bytes_sent) / time_delta / 1024 / 1024
                network_recv_speed = (current_network_io.bytes_recv - self.last_network_io.bytes_recv) / time_delta / 1024 / 1024
                
                self.network_sent_label.setText(f"发送: {network_sent_speed:.2f} MB/s")
                self.network_recv_label.setText(f"接收: {network_recv_speed:.2f} MB/s")
            
            # 保存历史数据
            if time_delta > 0:
                data_point = {
                    'timestamp': datetime.now(),
                    'cpu': cpu_percent,
                    'memory': memory_percent,
                    'disk_read': disk_read_speed if self.last_disk_io else 0,
                    'disk_write': disk_write_speed if self.last_disk_io else 0,
                    'network_total': (network_sent_speed + network_recv_speed) if self.last_network_io else 0
                }
                
                self.performance_data.append(data_point)
                
                # 限制历史数据大小
                if len(self.performance_data) > self.max_data_points:
                    self.performance_data = self.performance_data[-self.max_data_points:]
                
                # 更新历史表格
                self.update_history_table()
            
            # 更新基准值
            self.last_disk_io = current_disk_io
            self.last_network_io = current_network_io
            self.last_time = current_time
            
        except Exception as e:
            print(f"性能监控更新失败: {e}")
            
    def update_history_table(self):
        """更新历史数据表格"""
        self.history_table.setRowCount(len(self.performance_data))
        
        for i, data in enumerate(self.performance_data):
            self.history_table.setItem(i, 0, QTableWidgetItem(data['timestamp'].strftime("%H:%M:%S")))
            self.history_table.setItem(i, 1, QTableWidgetItem(f"{data['cpu']:.1f}"))
            self.history_table.setItem(i, 2, QTableWidgetItem(f"{data['memory']:.1f}"))
            self.history_table.setItem(i, 3, QTableWidgetItem(f"{data['disk_read']:.2f}"))
            self.history_table.setItem(i, 4, QTableWidgetItem(f"{data['disk_write']:.2f}"))
            self.history_table.setItem(i, 5, QTableWidgetItem(f"{data['network_total']:.2f}"))
        
        # 滚动到最新数据
        self.history_table.scrollToBottom()
        
    def update_system_info(self):
        """更新系统信息"""
        try:
            info = []
            info.append("=== 系统信息 ===")
            info.append(f"操作系统: {psutil.WINDOWS if psutil.WINDOWS else 'Unix-like'}")
            info.append(f"CPU核心数: {psutil.cpu_count(logical=False)} 物理核心, {psutil.cpu_count(logical=True)} 逻辑核心")
            
            memory = psutil.virtual_memory()
            info.append(f"总内存: {memory.total // 1024 // 1024 // 1024} GB")
            
            disk = psutil.disk_usage('/')
            info.append(f"磁盘空间: {disk.total // 1024 // 1024 // 1024} GB 总计, {disk.free // 1024 // 1024 // 1024} GB 可用")
            
            info.append("\n=== 进程信息 ===")
            process = psutil.Process()
            info.append(f"进程ID: {process.pid}")
            info.append(f"进程名称: {process.name()}")
            info.append(f"创建时间: {datetime.fromtimestamp(process.create_time()).strftime('%Y-%m-%d %H:%M:%S')}")
            
            memory_info = process.memory_info()
            info.append(f"内存使用: {memory_info.rss // 1024 // 1024} MB")
            
            self.system_text.setText("\n".join(info))
            
        except Exception as e:
            self.system_text.setText(f"获取系统信息失败: {e}")
            
    def refresh_performance(self):
        """刷新性能数据"""
        self.update_performance()
        self.update_system_info()
        
    def clear_history(self):
        """清除历史数据"""
        self.performance_data.clear()
        self.history_table.setRowCount(0)
        
    def export_report(self):
        """导出性能报告"""
        from PyQt5.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出性能报告", f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("视频融合编辑器性能报告\n")
                    f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 50 + "\n\n")
                    
                    # 系统信息
                    f.write(self.system_text.toPlainText())
                    f.write("\n\n=== 性能历史数据 ===\n")
                    f.write("时间\t\tCPU(%)\t内存(%)\t磁盘读(MB/s)\t磁盘写(MB/s)\t网络(MB/s)\n")
                    
                    for data in self.performance_data:
                        f.write(f"{data['timestamp'].strftime('%H:%M:%S')}\t\t"
                               f"{data['cpu']:.1f}\t{data['memory']:.1f}\t"
                               f"{data['disk_read']:.2f}\t\t{data['disk_write']:.2f}\t\t"
                               f"{data['network_total']:.2f}\n")
                
                self.performance_exported.emit(file_path)
                
            except Exception as e:
                print(f"导出性能报告失败: {e}")
                
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.hide()  # 隐藏而不是关闭，以便重复使用
        event.ignore()
