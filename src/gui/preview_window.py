"""
预览窗口类
Preview Window Class
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, 
                             QLabel, QPushButton, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QImage
import cv2


class PreviewWindow(QDialog):
    """预览窗口"""
    
    # 信号定义
    preview_requested = pyqtSignal()
    preview_cleared = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("融合预览")
        self.setModal(False)  # 非模态窗口
        self.resize(600, 500)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 预览组
        preview_group = QGroupBox("融合预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel("点击'生成预览'查看融合效果")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumHeight(300)
        self.preview_label.setStyleSheet("border: 1px solid #ccc; background-color: #f5f5f5;")
        preview_layout.addWidget(self.preview_label)
        
        # 预览控制按钮
        preview_btn_layout = QHBoxLayout()
        self.preview_btn = QPushButton("生成预览")
        self.preview_btn.clicked.connect(self.request_preview)
        preview_btn_layout.addWidget(self.preview_btn)
        
        self.clear_preview_btn = QPushButton("清除预览")
        self.clear_preview_btn.clicked.connect(self.clear_preview)
        preview_btn_layout.addWidget(self.clear_preview_btn)
        
        self.save_preview_btn = QPushButton("保存预览")
        self.save_preview_btn.clicked.connect(self.save_preview)
        self.save_preview_btn.setEnabled(False)
        preview_btn_layout.addWidget(self.save_preview_btn)
        
        preview_btn_layout.addStretch()
        preview_layout.addLayout(preview_btn_layout)
        
        layout.addWidget(preview_group)
        
        # 预览信息
        info_group = QGroupBox("预览信息")
        info_layout = QVBoxLayout(info_group)
        
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(100)
        self.info_text.setReadOnly(True)
        self.info_text.setStyleSheet("font-family: monospace; font-size: 9pt;")
        info_layout.addWidget(self.info_text)
        
        layout.addWidget(info_group)
        
        # 关闭按钮
        close_btn_layout = QHBoxLayout()
        close_btn_layout.addStretch()
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        close_btn_layout.addWidget(close_btn)
        
        layout.addLayout(close_btn_layout)
        
    def request_preview(self):
        """请求生成预览"""
        self.preview_requested.emit()
        
    def clear_preview(self):
        """清除预览"""
        self.preview_label.clear()
        self.preview_label.setText("点击'生成预览'查看融合效果")
        self.save_preview_btn.setEnabled(False)
        self.info_text.clear()
        self.preview_cleared.emit()
        
    def save_preview(self):
        """保存预览图像"""
        if hasattr(self, 'current_pixmap') and self.current_pixmap:
            from PyQt5.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存预览图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp)"
            )
            if file_path:
                self.current_pixmap.save(file_path)
                self.add_info(f"预览图像已保存: {file_path}")
        
    def update_preview(self, frame, frame_index=None):
        """更新预览图像"""
        try:
            if frame is not None:
                # 转换为QPixmap并显示
                height, width, channel = frame.shape
                bytes_per_line = 3 * width
                q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
                
                pixmap = QPixmap.fromImage(q_image)
                
                # 缩放图像以适应标签大小
                scaled_pixmap = pixmap.scaled(
                    self.preview_label.size(), 
                    Qt.KeepAspectRatio, 
                    Qt.SmoothTransformation
                )
                
                self.preview_label.setPixmap(scaled_pixmap)
                self.current_pixmap = pixmap
                self.save_preview_btn.setEnabled(True)
                
                # 更新信息
                info = f"预览更新成功\n"
                info += f"图像尺寸: {width}x{height}\n"
                if frame_index is not None:
                    info += f"帧索引: {frame_index}\n"
                self.info_text.setText(info)
                
        except Exception as e:
            self.add_info(f"预览更新失败: {e}")
            
    def add_info(self, message):
        """添加信息"""
        self.info_text.append(message)
        
    def set_preview_enabled(self, enabled):
        """设置预览按钮状态"""
        self.preview_btn.setEnabled(enabled)
        
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.hide()  # 隐藏而不是关闭，以便重复使用
        event.ignore()
