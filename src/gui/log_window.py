"""
日志窗口类
Log Window Class
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGroupBox, 
                             QTextEdit, QPushButton, QCheckBox, QComboBox, QLabel)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
import logging
from datetime import datetime


class LogWindow(QDialog):
    """日志窗口"""
    
    # 信号定义
    log_cleared = pyqtSignal()
    log_exported = pyqtSignal(str)  # 导出路径
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("操作日志")
        self.setModal(False)  # 非模态窗口
        self.resize(800, 600)
        
        self.log_buffer = []  # 日志缓冲区
        self.max_log_lines = 1000  # 最大日志行数
        
        self.init_ui()
        self.setup_auto_refresh()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 日志控制组
        control_group = QGroupBox("日志控制")
        control_layout = QHBoxLayout(control_group)
        
        # 日志级别过滤
        level_label = QLabel("级别:")
        control_layout.addWidget(level_label)
        
        self.level_combo = QComboBox()
        self.level_combo.addItems(["全部", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.level_combo.setCurrentText("INFO")
        self.level_combo.currentTextChanged.connect(self.filter_logs)
        control_layout.addWidget(self.level_combo)
        
        control_layout.addStretch()
        
        # 自动刷新
        self.auto_refresh_cb = QCheckBox("自动刷新")
        self.auto_refresh_cb.setChecked(True)
        self.auto_refresh_cb.toggled.connect(self.toggle_auto_refresh)
        control_layout.addWidget(self.auto_refresh_cb)
        
        # 自动滚动
        self.auto_scroll_cb = QCheckBox("自动滚动")
        self.auto_scroll_cb.setChecked(True)
        control_layout.addWidget(self.auto_scroll_cb)
        
        layout.addWidget(control_group)
        
        # 日志显示组
        log_group = QGroupBox("日志内容")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #d4d4d4;
                border: 1px solid #3c3c3c;
            }
        """)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 按钮组
        btn_layout = QHBoxLayout()
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_logs)
        btn_layout.addWidget(refresh_btn)
        
        # 清除按钮
        clear_btn = QPushButton("清除日志")
        clear_btn.clicked.connect(self.clear_logs)
        btn_layout.addWidget(clear_btn)
        
        # 导出按钮
        export_btn = QPushButton("导出日志")
        export_btn.clicked.connect(self.export_logs)
        btn_layout.addWidget(export_btn)
        
        btn_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        btn_layout.addWidget(close_btn)
        
        layout.addLayout(btn_layout)
        
    def setup_auto_refresh(self):
        """设置自动刷新"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_logs)
        self.refresh_timer.start(1000)  # 每秒刷新一次
        
    def toggle_auto_refresh(self, enabled):
        """切换自动刷新"""
        if enabled:
            self.refresh_timer.start(1000)
        else:
            self.refresh_timer.stop()
            
    def add_log(self, level, message, timestamp=None):
        """添加日志"""
        if timestamp is None:
            timestamp = datetime.now()
            
        log_entry = {
            'timestamp': timestamp,
            'level': level,
            'message': message
        }
        
        self.log_buffer.append(log_entry)
        
        # 限制日志缓冲区大小
        if len(self.log_buffer) > self.max_log_lines:
            self.log_buffer = self.log_buffer[-self.max_log_lines:]
            
        # 如果窗口可见且自动刷新开启，立即更新显示
        if self.isVisible() and self.auto_refresh_cb.isChecked():
            self.refresh_logs()
            
    def filter_logs(self):
        """过滤日志"""
        self.refresh_logs()
        
    def refresh_logs(self):
        """刷新日志显示"""
        selected_level = self.level_combo.currentText()
        
        # 清空当前显示
        self.log_text.clear()
        
        # 过滤并显示日志
        for entry in self.log_buffer:
            if selected_level == "全部" or entry['level'] == selected_level:
                timestamp_str = entry['timestamp'].strftime("%H:%M:%S")
                level_str = entry['level'].ljust(8)
                
                # 根据日志级别设置颜色
                color = self.get_level_color(entry['level'])
                
                formatted_log = f"<span style='color: #888;'>{timestamp_str}</span> " \
                               f"<span style='color: {color}; font-weight: bold;'>{level_str}</span> " \
                               f"<span style='color: #d4d4d4;'>{entry['message']}</span>"
                
                self.log_text.append(formatted_log)
        
        # 自动滚动到底部
        if self.auto_scroll_cb.isChecked():
            scrollbar = self.log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
    def get_level_color(self, level):
        """获取日志级别对应的颜色"""
        colors = {
            'DEBUG': '#6a9955',
            'INFO': '#4fc1ff',
            'WARNING': '#ffcc02',
            'ERROR': '#f44747',
            'CRITICAL': '#ff6b6b'
        }
        return colors.get(level, '#d4d4d4')
        
    def clear_logs(self):
        """清除日志"""
        self.log_buffer.clear()
        self.log_text.clear()
        self.log_cleared.emit()
        
    def export_logs(self):
        """导出日志"""
        from PyQt5.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出日志", f"log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"视频融合编辑器日志导出\n")
                    f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 50 + "\n\n")
                    
                    for entry in self.log_buffer:
                        timestamp_str = entry['timestamp'].strftime("%Y-%m-%d %H:%M:%S")
                        f.write(f"[{timestamp_str}] {entry['level']}: {entry['message']}\n")
                
                self.add_log("INFO", f"日志已导出到: {file_path}")
                self.log_exported.emit(file_path)
                
            except Exception as e:
                self.add_log("ERROR", f"导出日志失败: {e}")
                
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.hide()  # 隐藏而不是关闭，以便重复使用
        event.ignore()
