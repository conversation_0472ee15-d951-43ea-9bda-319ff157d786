"""
控制面板组件
Control Panel Component
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                             QLabel, QSlider, QSpinBox, QComboBox, QPushButton,
                             QCheckBox, QDoubleSpinBox, QLineEdit, QTextEdit,
                             QTabWidget, QScrollArea, QFrame, QColorDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QColor, QPalette

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager


class ControlPanel(QWidget):
    """控制面板组件"""
    
    # 信号定义
    fusion_params_changed = pyqtSignal(dict)  # 融合参数改变
    parameters_changed = pyqtSignal(dict)     # 参数改变（兼容性）
    preview_requested = pyqtSignal()          # 预览请求（兼容性）
    fusion_requested = pyqtSignal()           # 融合请求（兼容性）
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.logger = Logger.get_logger(__name__)
        self.config = ConfigManager()
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 创建标签页控件
        self.tab_widget = QTabWidget()

        # 基础融合标签页
        self.create_basic_fusion_tab()

        # 高级融合标签页
        self.create_advanced_fusion_tab()

        # 预处理标签页
        self.create_preprocessing_tab()

        # 文字叠加标签页
        self.create_text_overlay_tab()

        # 输出设置标签页
        self.create_output_settings_tab()

        layout.addWidget(self.tab_widget)

        # 实时预览控制
        self.create_preview_controls(layout)
    
    def create_basic_fusion_tab(self):
        """创建基础融合标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 融合类型选择
        fusion_type_group = QGroupBox("融合类型")
        fusion_type_layout = QVBoxLayout(fusion_type_group)

        self.fusion_type_combo = QComboBox()
        self.fusion_type_combo.addItems([
            "插入融合 (Insertion)",
            "叠加融合 (Overlay)",
            "混合融合 (Blend)"
        ])
        self.fusion_type_combo.currentTextChanged.connect(self.on_fusion_type_changed)
        fusion_type_layout.addWidget(self.fusion_type_combo)
        layout.addWidget(fusion_type_group)

        # 插入模式选择（仅插入融合时显示）
        self.insertion_mode_group = QGroupBox("插入模式")
        insertion_mode_layout = QVBoxLayout(self.insertion_mode_group)

        self.insertion_mode_combo = QComboBox()
        self.insertion_mode_combo.addItems([
            "直接插入 (Direct)",
            "替换插入 (Replace)",
            "分段插入 (Segment)"
        ])
        self.insertion_mode_combo.currentTextChanged.connect(self.on_params_changed)
        insertion_mode_layout.addWidget(self.insertion_mode_combo)
        layout.addWidget(self.insertion_mode_group)

        # 基础参数
        self.create_basic_params_group(layout)

        layout.addStretch()
        self.tab_widget.addTab(tab, "基础融合")
    
    def create_basic_params_group(self, parent_layout):
        """创建基础参数组"""
        group = QGroupBox("基础参数")
        layout = QVBoxLayout(group)

        # 透明度控制
        alpha_layout = QHBoxLayout()
        alpha_layout.addWidget(QLabel("透明度:"))
        self.alpha_slider = QSlider(Qt.Horizontal)
        self.alpha_slider.setMinimum(0)
        self.alpha_slider.setMaximum(100)
        self.alpha_slider.setValue(int(self.config.get("fusion.default_blend_alpha", 0.5) * 100))
        self.alpha_slider.valueChanged.connect(self.on_params_changed)
        alpha_layout.addWidget(self.alpha_slider)

        self.alpha_label = QLabel("50%")
        self.alpha_slider.valueChanged.connect(lambda v: self.alpha_label.setText(f"{v}%"))
        alpha_layout.addWidget(self.alpha_label)
        layout.addLayout(alpha_layout)

        # 调整模式
        resize_layout = QHBoxLayout()
        resize_layout.addWidget(QLabel("调整模式:"))
        self.resize_mode_combo = QComboBox()
        self.resize_mode_combo.addItems(["适应 (fit)", "拉伸 (stretch)", "裁剪 (crop)", "原始 (original)"])
        self.resize_mode_combo.currentTextChanged.connect(self.on_params_changed)
        resize_layout.addWidget(self.resize_mode_combo)
        layout.addLayout(resize_layout)

        # 帧范围控制
        frame_range_layout = QHBoxLayout()
        frame_range_layout.addWidget(QLabel("帧范围:"))
        self.start_frame_spinbox = QSpinBox()
        self.start_frame_spinbox.setMinimum(0)
        self.start_frame_spinbox.setMaximum(99999)
        self.start_frame_spinbox.setSuffix(" 开始")
        self.start_frame_spinbox.valueChanged.connect(self.on_params_changed)
        frame_range_layout.addWidget(self.start_frame_spinbox)

        self.end_frame_spinbox = QSpinBox()
        self.end_frame_spinbox.setMinimum(1)
        self.end_frame_spinbox.setMaximum(99999)
        self.end_frame_spinbox.setValue(100)
        self.end_frame_spinbox.setSuffix(" 结束")
        self.end_frame_spinbox.valueChanged.connect(self.on_params_changed)
        frame_range_layout.addWidget(self.end_frame_spinbox)
        layout.addLayout(frame_range_layout)

        parent_layout.addWidget(group)

    def create_advanced_fusion_tab(self):
        """创建高级融合标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 叠加融合参数
        self.overlay_group = QGroupBox("叠加融合参数")
        overlay_layout = QVBoxLayout(self.overlay_group)

        # 叠加模式
        overlay_mode_layout = QHBoxLayout()
        overlay_mode_layout.addWidget(QLabel("叠加模式:"))
        self.overlay_mode_combo = QComboBox()
        self.overlay_mode_combo.addItems([
            "正常 (Normal)", "正片叠底 (Multiply)", "滤色 (Screen)",
            "叠加 (Overlay)", "柔光 (Soft Light)", "强光 (Hard Light)"
        ])
        self.overlay_mode_combo.currentTextChanged.connect(self.on_params_changed)
        overlay_mode_layout.addWidget(self.overlay_mode_combo)
        overlay_layout.addLayout(overlay_mode_layout)

        # 叠加位置
        position_layout = QHBoxLayout()
        position_layout.addWidget(QLabel("位置:"))
        self.overlay_x_spinbox = QSpinBox()
        self.overlay_x_spinbox.setMinimum(0)
        self.overlay_x_spinbox.setMaximum(9999)
        self.overlay_x_spinbox.setSuffix(" X")
        self.overlay_x_spinbox.valueChanged.connect(self.on_params_changed)
        position_layout.addWidget(self.overlay_x_spinbox)

        self.overlay_y_spinbox = QSpinBox()
        self.overlay_y_spinbox.setMinimum(0)
        self.overlay_y_spinbox.setMaximum(9999)
        self.overlay_y_spinbox.setSuffix(" Y")
        self.overlay_y_spinbox.valueChanged.connect(self.on_params_changed)
        position_layout.addWidget(self.overlay_y_spinbox)
        overlay_layout.addLayout(position_layout)

        # 叠加尺寸
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("尺寸:"))
        self.overlay_width_spinbox = QSpinBox()
        self.overlay_width_spinbox.setMinimum(1)
        self.overlay_width_spinbox.setMaximum(9999)
        self.overlay_width_spinbox.setValue(200)
        self.overlay_width_spinbox.setSuffix(" 宽")
        self.overlay_width_spinbox.valueChanged.connect(self.on_params_changed)
        size_layout.addWidget(self.overlay_width_spinbox)

        self.overlay_height_spinbox = QSpinBox()
        self.overlay_height_spinbox.setMinimum(1)
        self.overlay_height_spinbox.setMaximum(9999)
        self.overlay_height_spinbox.setValue(150)
        self.overlay_height_spinbox.setSuffix(" 高")
        self.overlay_height_spinbox.valueChanged.connect(self.on_params_changed)
        size_layout.addWidget(self.overlay_height_spinbox)
        overlay_layout.addLayout(size_layout)

        # 移动轨迹
        self.overlay_moving_cb = QCheckBox("启用移动轨迹")
        self.overlay_moving_cb.stateChanged.connect(self.on_params_changed)
        overlay_layout.addWidget(self.overlay_moving_cb)

        layout.addWidget(self.overlay_group)

        # 混合融合参数
        self.blend_group = QGroupBox("混合融合参数")
        blend_layout = QVBoxLayout(self.blend_group)

        # 混合模式
        blend_mode_layout = QHBoxLayout()
        blend_mode_layout.addWidget(QLabel("混合模式:"))
        self.blend_mode_combo = QComboBox()
        self.blend_mode_combo.addItems([
            "线性 (Linear)", "加权 (Weighted)", "Alpha混合 (Alpha Blend)",
            "羽化 (Feather)", "渐变 (Gradient)"
        ])
        self.blend_mode_combo.currentTextChanged.connect(self.on_params_changed)
        blend_mode_layout.addWidget(self.blend_mode_combo)
        blend_layout.addLayout(blend_mode_layout)

        # 羽化半径
        feather_layout = QHBoxLayout()
        feather_layout.addWidget(QLabel("羽化半径:"))
        self.feather_radius_spinbox = QSpinBox()
        self.feather_radius_spinbox.setMinimum(0)
        self.feather_radius_spinbox.setMaximum(50)
        self.feather_radius_spinbox.setValue(5)
        self.feather_radius_spinbox.valueChanged.connect(self.on_params_changed)
        feather_layout.addWidget(self.feather_radius_spinbox)
        blend_layout.addLayout(feather_layout)

        layout.addWidget(self.blend_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "高级融合")
    
    def create_preprocessing_tab(self):
        """创建预处理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 边缘检测组
        edge_group = QGroupBox("边缘检测")
        edge_layout = QVBoxLayout(edge_group)

        self.edge_detection_cb = QCheckBox("启用边缘检测")
        self.edge_detection_cb.stateChanged.connect(self.on_params_changed)
        edge_layout.addWidget(self.edge_detection_cb)

        # 边缘检测方法
        edge_method_layout = QHBoxLayout()
        edge_method_layout.addWidget(QLabel("检测方法:"))
        self.edge_method_combo = QComboBox()
        self.edge_method_combo.addItems(["Canny", "Sobel", "Laplacian", "Scharr"])
        self.edge_method_combo.currentTextChanged.connect(self.on_params_changed)
        edge_method_layout.addWidget(self.edge_method_combo)
        edge_layout.addLayout(edge_method_layout)

        # 边缘检测阈值
        edge_threshold_layout = QHBoxLayout()
        edge_threshold_layout.addWidget(QLabel("低阈值:"))
        self.edge_low_threshold_spinbox = QSpinBox()
        self.edge_low_threshold_spinbox.setMinimum(1)
        self.edge_low_threshold_spinbox.setMaximum(255)
        self.edge_low_threshold_spinbox.setValue(50)
        self.edge_low_threshold_spinbox.valueChanged.connect(self.on_params_changed)
        edge_threshold_layout.addWidget(self.edge_low_threshold_spinbox)

        edge_threshold_layout.addWidget(QLabel("高阈值:"))
        self.edge_high_threshold_spinbox = QSpinBox()
        self.edge_high_threshold_spinbox.setMinimum(1)
        self.edge_high_threshold_spinbox.setMaximum(255)
        self.edge_high_threshold_spinbox.setValue(150)
        self.edge_high_threshold_spinbox.valueChanged.connect(self.on_params_changed)
        edge_threshold_layout.addWidget(self.edge_high_threshold_spinbox)
        edge_layout.addLayout(edge_threshold_layout)

        layout.addWidget(edge_group)

        # 直方图处理组
        histogram_group = QGroupBox("直方图处理")
        histogram_layout = QVBoxLayout(histogram_group)

        self.histogram_match_cb = QCheckBox("启用直方图匹配")
        self.histogram_match_cb.stateChanged.connect(self.on_params_changed)
        histogram_layout.addWidget(self.histogram_match_cb)

        self.histogram_equalize_cb = QCheckBox("启用直方图均衡化")
        self.histogram_equalize_cb.stateChanged.connect(self.on_params_changed)
        histogram_layout.addWidget(self.histogram_equalize_cb)

        # Gamma校正
        gamma_layout = QHBoxLayout()
        gamma_layout.addWidget(QLabel("Gamma校正:"))
        self.gamma_spinbox = QDoubleSpinBox()
        self.gamma_spinbox.setMinimum(0.1)
        self.gamma_spinbox.setMaximum(3.0)
        self.gamma_spinbox.setValue(1.0)
        self.gamma_spinbox.setSingleStep(0.1)
        self.gamma_spinbox.valueChanged.connect(self.on_params_changed)
        gamma_layout.addWidget(self.gamma_spinbox)
        histogram_layout.addLayout(gamma_layout)

        layout.addWidget(histogram_group)

        # 图像滤镜组
        filter_group = QGroupBox("图像滤镜")
        filter_layout = QVBoxLayout(filter_group)

        # 滤镜类型
        filter_type_layout = QHBoxLayout()
        filter_type_layout.addWidget(QLabel("滤镜类型:"))
        self.filter_type_combo = QComboBox()
        self.filter_type_combo.addItems([
            "无", "高斯模糊", "运动模糊", "中值模糊",
            "双边滤波", "锐化", "浮雕", "降噪"
        ])
        self.filter_type_combo.currentTextChanged.connect(self.on_params_changed)
        filter_type_layout.addWidget(self.filter_type_combo)
        filter_layout.addLayout(filter_type_layout)

        # 滤镜强度
        filter_strength_layout = QHBoxLayout()
        filter_strength_layout.addWidget(QLabel("滤镜强度:"))
        self.filter_strength_slider = QSlider(Qt.Horizontal)
        self.filter_strength_slider.setMinimum(1)
        self.filter_strength_slider.setMaximum(20)
        self.filter_strength_slider.setValue(5)
        self.filter_strength_slider.valueChanged.connect(self.on_params_changed)
        filter_strength_layout.addWidget(self.filter_strength_slider)

        self.filter_strength_label = QLabel("5")
        self.filter_strength_slider.valueChanged.connect(
            lambda v: self.filter_strength_label.setText(str(v))
        )
        filter_strength_layout.addWidget(self.filter_strength_label)
        filter_layout.addLayout(filter_strength_layout)

        layout.addWidget(filter_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "预处理")
    
    def create_text_overlay_tab(self):
        """创建文字叠加标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 文字内容组
        text_content_group = QGroupBox("文字内容")
        text_content_layout = QVBoxLayout(text_content_group)

        self.text_overlay_cb = QCheckBox("启用文字叠加")
        self.text_overlay_cb.stateChanged.connect(self.on_params_changed)
        text_content_layout.addWidget(self.text_overlay_cb)

        # 文字输入
        text_content_layout.addWidget(QLabel("文字内容:"))
        self.text_content_edit = QLineEdit()
        self.text_content_edit.setPlaceholderText("请输入要叠加的文字...")
        self.text_content_edit.textChanged.connect(self.on_params_changed)
        text_content_layout.addWidget(self.text_content_edit)

        layout.addWidget(text_content_group)

        # 文字样式组
        text_style_group = QGroupBox("文字样式")
        text_style_layout = QVBoxLayout(text_style_group)

        # 样式预设
        style_preset_layout = QHBoxLayout()
        style_preset_layout.addWidget(QLabel("样式预设:"))
        self.text_style_combo = QComboBox()
        self.text_style_combo.addItems([
            "默认", "标题", "副标题", "水印", "警告", "自定义"
        ])
        self.text_style_combo.currentTextChanged.connect(self.on_text_style_changed)
        style_preset_layout.addWidget(self.text_style_combo)
        text_style_layout.addLayout(style_preset_layout)

        # 字体大小
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(QLabel("字体大小:"))
        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setMinimum(8)
        self.font_size_spinbox.setMaximum(72)
        self.font_size_spinbox.setValue(24)
        self.font_size_spinbox.valueChanged.connect(self.on_params_changed)
        font_size_layout.addWidget(self.font_size_spinbox)
        text_style_layout.addLayout(font_size_layout)

        # 文字颜色
        color_layout = QHBoxLayout()
        color_layout.addWidget(QLabel("文字颜色:"))
        self.text_color_button = QPushButton()
        self.text_color_button.setFixedSize(50, 30)
        self.text_color_button.setStyleSheet("background-color: white; border: 1px solid black;")
        self.text_color_button.clicked.connect(self.choose_text_color)
        self.text_color = QColor(255, 255, 255)  # 默认白色
        color_layout.addWidget(self.text_color_button)
        text_style_layout.addLayout(color_layout)

        # 文字透明度
        text_alpha_layout = QHBoxLayout()
        text_alpha_layout.addWidget(QLabel("文字透明度:"))
        self.text_alpha_slider = QSlider(Qt.Horizontal)
        self.text_alpha_slider.setMinimum(0)
        self.text_alpha_slider.setMaximum(100)
        self.text_alpha_slider.setValue(100)
        self.text_alpha_slider.valueChanged.connect(self.on_params_changed)
        text_alpha_layout.addWidget(self.text_alpha_slider)

        self.text_alpha_label = QLabel("100%")
        self.text_alpha_slider.valueChanged.connect(
            lambda v: self.text_alpha_label.setText(f"{v}%")
        )
        text_alpha_layout.addWidget(self.text_alpha_label)
        text_style_layout.addLayout(text_alpha_layout)

        layout.addWidget(text_style_group)

        # 文字位置组
        text_position_group = QGroupBox("文字位置")
        text_position_layout = QVBoxLayout(text_position_group)

        # 位置预设
        position_preset_layout = QHBoxLayout()
        position_preset_layout.addWidget(QLabel("位置预设:"))
        self.text_position_combo = QComboBox()
        self.text_position_combo.addItems([
            "左上", "上中", "右上", "左中", "中心",
            "右中", "左下", "下中", "右下", "自定义"
        ])
        self.text_position_combo.currentTextChanged.connect(self.on_text_position_changed)
        position_preset_layout.addWidget(self.text_position_combo)
        text_position_layout.addLayout(position_preset_layout)

        # 自定义位置
        custom_pos_layout = QHBoxLayout()
        custom_pos_layout.addWidget(QLabel("自定义位置:"))
        self.text_x_spinbox = QSpinBox()
        self.text_x_spinbox.setMinimum(0)
        self.text_x_spinbox.setMaximum(9999)
        self.text_x_spinbox.setSuffix(" X")
        self.text_x_spinbox.valueChanged.connect(self.on_params_changed)
        custom_pos_layout.addWidget(self.text_x_spinbox)

        self.text_y_spinbox = QSpinBox()
        self.text_y_spinbox.setMinimum(0)
        self.text_y_spinbox.setMaximum(9999)
        self.text_y_spinbox.setSuffix(" Y")
        self.text_y_spinbox.valueChanged.connect(self.on_params_changed)
        custom_pos_layout.addWidget(self.text_y_spinbox)
        text_position_layout.addLayout(custom_pos_layout)

        # 文字动画
        self.text_animation_cb = QCheckBox("启用文字动画")
        self.text_animation_cb.stateChanged.connect(self.on_params_changed)
        text_position_layout.addWidget(self.text_animation_cb)

        layout.addWidget(text_position_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "文字叠加")

    def create_output_settings_tab(self):
        """创建输出设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 输出格式组
        output_format_group = QGroupBox("输出格式")
        output_format_layout = QVBoxLayout(output_format_group)

        # 视频编码
        codec_layout = QHBoxLayout()
        codec_layout.addWidget(QLabel("视频编码:"))
        self.output_codec_combo = QComboBox()
        self.output_codec_combo.addItems(["mp4v", "XVID", "H264", "MJPG"])
        self.output_codec_combo.currentTextChanged.connect(self.on_params_changed)
        codec_layout.addWidget(self.output_codec_combo)
        output_format_layout.addLayout(codec_layout)

        # 输出帧率
        fps_layout = QHBoxLayout()
        fps_layout.addWidget(QLabel("输出帧率:"))
        self.output_fps_spinbox = QDoubleSpinBox()
        self.output_fps_spinbox.setMinimum(1.0)
        self.output_fps_spinbox.setMaximum(120.0)
        self.output_fps_spinbox.setValue(30.0)
        self.output_fps_spinbox.setSingleStep(1.0)
        self.output_fps_spinbox.setSuffix(" fps")
        self.output_fps_spinbox.valueChanged.connect(self.on_params_changed)
        fps_layout.addWidget(self.output_fps_spinbox)
        output_format_layout.addLayout(fps_layout)

        # 输出质量
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("输出质量:"))
        self.output_quality_slider = QSlider(Qt.Horizontal)
        self.output_quality_slider.setMinimum(1)
        self.output_quality_slider.setMaximum(10)
        self.output_quality_slider.setValue(8)
        self.output_quality_slider.valueChanged.connect(self.on_params_changed)
        quality_layout.addWidget(self.output_quality_slider)

        self.output_quality_label = QLabel("8")
        self.output_quality_slider.valueChanged.connect(
            lambda v: self.output_quality_label.setText(str(v))
        )
        quality_layout.addWidget(self.output_quality_label)
        output_format_layout.addLayout(quality_layout)

        layout.addWidget(output_format_group)

        layout.addStretch()
        self.tab_widget.addTab(tab, "输出设置")
    
    def create_preview_controls(self, parent_layout):
        """创建预览控制"""
        preview_group = QGroupBox("实时预览")
        preview_layout = QVBoxLayout(preview_group)

        # 预览控制按钮
        preview_buttons_layout = QHBoxLayout()

        self.preview_button = QPushButton("生成预览")
        self.preview_button.clicked.connect(self.generate_preview)
        preview_buttons_layout.addWidget(self.preview_button)

        self.auto_preview_cb = QCheckBox("自动预览")
        self.auto_preview_cb.stateChanged.connect(self.on_auto_preview_changed)
        preview_buttons_layout.addWidget(self.auto_preview_cb)

        preview_layout.addLayout(preview_buttons_layout)

        # 预览帧数控制
        preview_frames_layout = QHBoxLayout()
        preview_frames_layout.addWidget(QLabel("预览帧数:"))
        self.preview_frames_spinbox = QSpinBox()
        self.preview_frames_spinbox.setMinimum(1)
        self.preview_frames_spinbox.setMaximum(10)
        self.preview_frames_spinbox.setValue(3)
        preview_frames_layout.addWidget(self.preview_frames_spinbox)
        preview_layout.addLayout(preview_frames_layout)

        parent_layout.addWidget(preview_group)

        # 设置自动预览定时器
        self.auto_preview_timer = QTimer()
        self.auto_preview_timer.setSingleShot(True)
        self.auto_preview_timer.timeout.connect(self.generate_preview)

    def on_fusion_type_changed(self, fusion_type: str):
        """融合类型改变"""
        self.logger.info(f"融合类型改变: {fusion_type}")

        # 根据融合类型显示/隐藏相关控件
        if "插入融合" in fusion_type:
            self.insertion_mode_group.setVisible(True)
            self.overlay_group.setVisible(False)
            self.blend_group.setVisible(False)
        elif "叠加融合" in fusion_type:
            self.insertion_mode_group.setVisible(False)
            self.overlay_group.setVisible(True)
            self.blend_group.setVisible(False)
        elif "混合融合" in fusion_type:
            self.insertion_mode_group.setVisible(False)
            self.overlay_group.setVisible(False)
            self.blend_group.setVisible(True)

        self.on_params_changed()

    def on_text_style_changed(self, style: str):
        """文字样式改变"""
        # 根据预设样式更新参数
        if style == "标题":
            self.font_size_spinbox.setValue(36)
            self.text_color = QColor(255, 255, 0)  # 黄色
        elif style == "副标题":
            self.font_size_spinbox.setValue(24)
            self.text_color = QColor(255, 255, 255)  # 白色
        elif style == "水印":
            self.font_size_spinbox.setValue(16)
            self.text_color = QColor(200, 200, 200)  # 浅灰色
            self.text_alpha_slider.setValue(70)
        elif style == "警告":
            self.font_size_spinbox.setValue(28)
            self.text_color = QColor(255, 0, 0)  # 红色

        # 更新颜色按钮显示
        self.update_color_button()
        self.on_params_changed()

    def on_text_position_changed(self, position: str):
        """文字位置改变"""
        # 启用/禁用自定义位置控件
        is_custom = position == "自定义"
        self.text_x_spinbox.setEnabled(is_custom)
        self.text_y_spinbox.setEnabled(is_custom)

        self.on_params_changed()

    def choose_text_color(self):
        """选择文字颜色"""
        color = QColorDialog.getColor(self.text_color, self, "选择文字颜色")
        if color.isValid():
            self.text_color = color
            self.update_color_button()
            self.on_params_changed()

    def update_color_button(self):
        """更新颜色按钮显示"""
        color_name = self.text_color.name()
        self.text_color_button.setStyleSheet(
            f"background-color: {color_name}; border: 1px solid black;"
        )

    def on_auto_preview_changed(self, state):
        """自动预览状态改变"""
        if state == Qt.Checked:
            self.on_params_changed()  # 立即生成预览

    def generate_preview(self):
        """生成预览"""
        params = self.get_current_params()
        self.fusion_params_changed.emit(params)
        self.parameters_changed.emit(params)  # 兼容性信号
        self.preview_requested.emit()         # 兼容性信号
        self.logger.info("生成融合预览")

    def request_fusion(self):
        """请求开始融合"""
        self.fusion_requested.emit()
        self.logger.info("请求开始融合")

    def on_params_changed(self):
        """参数改变"""
        params = self.get_current_params()
        self.fusion_params_changed.emit(params)
        self.parameters_changed.emit(params)  # 兼容性信号
        self.logger.debug(f"融合参数改变")

        # 如果启用了自动预览，延迟生成预览
        if hasattr(self, 'auto_preview_cb') and self.auto_preview_cb.isChecked():
            self.auto_preview_timer.stop()
            self.auto_preview_timer.start(1000)  # 1秒延迟

    def get_current_params(self) -> dict:
        """获取当前参数"""
        # 映射融合类型
        fusion_type_text = self.fusion_type_combo.currentText()
        if "插入融合" in fusion_type_text:
            fusion_type = "insertion"
        elif "叠加融合" in fusion_type_text:
            fusion_type = "overlay"
        elif "混合融合" in fusion_type_text:
            fusion_type = "blend"
        else:
            fusion_type = "insertion"  # 默认值

        params = {
            # 基础参数
            "fusion_type": fusion_type,
            "alpha": self.alpha_slider.value() / 100.0,
            "resize_mode": self.resize_mode_combo.currentText().split()[0],
            "frame_range": (self.start_frame_spinbox.value(), self.end_frame_spinbox.value()),

            # 插入融合参数
            "insertion_mode": self.insertion_mode_combo.currentText().split()[0],

            # 叠加融合参数
            "overlay_mode": self.overlay_mode_combo.currentText().split()[0],
            "overlay_position": {
                "x": self.overlay_x_spinbox.value(),
                "y": self.overlay_y_spinbox.value(),
                "width": self.overlay_width_spinbox.value(),
                "height": self.overlay_height_spinbox.value(),
                "moving": self.overlay_moving_cb.isChecked()
            },

            # 混合融合参数
            "blend_mode": self.blend_mode_combo.currentText().split()[0],
            "feather_radius": self.feather_radius_spinbox.value(),

            # 预处理参数
            "edge_detection": {
                "enabled": self.edge_detection_cb.isChecked(),
                "method": self.edge_method_combo.currentText(),
                "low_threshold": self.edge_low_threshold_spinbox.value(),
                "high_threshold": self.edge_high_threshold_spinbox.value()
            },
            "histogram_processing": {
                "match": self.histogram_match_cb.isChecked(),
                "equalize": self.histogram_equalize_cb.isChecked(),
                "gamma": self.gamma_spinbox.value()
            },
            "image_filter": {
                "type": self.filter_type_combo.currentText(),
                "strength": self.filter_strength_slider.value()
            },

            # 文字叠加参数
            "text_overlay": {
                "enabled": self.text_overlay_cb.isChecked(),
                "content": self.text_content_edit.text(),
                "style": self.text_style_combo.currentText(),
                "font_size": self.font_size_spinbox.value(),
                "color": (self.text_color.red(), self.text_color.green(), self.text_color.blue()),
                "alpha": self.text_alpha_slider.value() / 100.0,
                "position": self.text_position_combo.currentText(),
                "custom_position": (self.text_x_spinbox.value(), self.text_y_spinbox.value()),
                "animation": self.text_animation_cb.isChecked()
            },

            # 输出设置
            "output": {
                "codec": self.output_codec_combo.currentText(),
                "fps": self.output_fps_spinbox.value(),
                "quality": self.output_quality_slider.value()
            },

            # 预览设置
            "preview": {
                "frames": self.preview_frames_spinbox.value(),
                "auto": self.auto_preview_cb.isChecked()
            }
        }

        return params

    def set_params(self, params: dict):
        """设置参数"""
        try:
            # 基础参数
            if "fusion_type" in params:
                index = self.fusion_type_combo.findText(params["fusion_type"])
                if index >= 0:
                    self.fusion_type_combo.setCurrentIndex(index)

            if "alpha" in params:
                self.alpha_slider.setValue(int(params["alpha"] * 100))

            # 更多参数设置...
            self.logger.info("参数设置完成")

        except Exception as e:
            self.logger.error(f"设置参数失败: {e}")

    def reset_params(self):
        """重置参数到默认值"""
        try:
            # 重置所有控件到默认值
            self.fusion_type_combo.setCurrentIndex(0)
            self.alpha_slider.setValue(50)
            self.resize_mode_combo.setCurrentIndex(0)
            self.start_frame_spinbox.setValue(0)
            self.end_frame_spinbox.setValue(100)

            # 重置其他控件...
            self.logger.info("参数重置完成")

        except Exception as e:
            self.logger.error(f"重置参数失败: {e}")

    def save_preset(self, name: str):
        """保存参数预设"""
        try:
            params = self.get_current_params()
            self.config.set(f"presets.{name}", params)
            self.config.save()
            self.logger.info(f"参数预设已保存: {name}")

        except Exception as e:
            self.logger.error(f"保存参数预设失败: {e}")

    def load_preset(self, name: str):
        """加载参数预设"""
        try:
            params = self.config.get(f"presets.{name}")
            if params:
                self.set_params(params)
                self.logger.info(f"参数预设已加载: {name}")
            else:
                self.logger.warning(f"参数预设不存在: {name}")

        except Exception as e:
            self.logger.error(f"加载参数预设失败: {e}")
