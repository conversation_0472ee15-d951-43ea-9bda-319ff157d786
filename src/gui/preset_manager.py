"""
参数预设管理器
Preset Manager
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QListWidget, 
                             QPushButton, QLineEdit, QLabel, QMessageBox,
                             QGroupBox, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal
import json
from typing import Dict, Any

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager


class PresetManager(QDialog):
    """参数预设管理器"""
    
    # 信号定义
    preset_loaded = pyqtSignal(dict)  # 预设加载信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.logger = Logger.get_logger(__name__)
        self.config = ConfigManager()
        
        self.setWindowTitle("参数预设管理器")
        self.setModal(True)
        self.resize(600, 400)
        
        self.init_ui()
        self.load_presets()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 预设列表组
        preset_list_group = QGroupBox("预设列表")
        preset_list_layout = QVBoxLayout(preset_list_group)
        
        self.preset_list = QListWidget()
        self.preset_list.itemClicked.connect(self.on_preset_selected)
        self.preset_list.itemDoubleClicked.connect(self.load_selected_preset)
        preset_list_layout.addWidget(self.preset_list)
        
        layout.addWidget(preset_list_group)
        
        # 预设详情组
        preset_detail_group = QGroupBox("预设详情")
        preset_detail_layout = QVBoxLayout(preset_detail_group)
        
        self.preset_detail_text = QTextEdit()
        self.preset_detail_text.setReadOnly(True)
        self.preset_detail_text.setMaximumHeight(150)
        preset_detail_layout.addWidget(self.preset_detail_text)
        
        layout.addWidget(preset_detail_group)
        
        # 新建预设组
        new_preset_group = QGroupBox("新建预设")
        new_preset_layout = QVBoxLayout(new_preset_group)
        
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("预设名称:"))
        self.preset_name_edit = QLineEdit()
        self.preset_name_edit.setPlaceholderText("请输入预设名称...")
        name_layout.addWidget(self.preset_name_edit)
        new_preset_layout.addLayout(name_layout)
        
        layout.addWidget(new_preset_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.load_button = QPushButton("加载预设")
        self.load_button.clicked.connect(self.load_selected_preset)
        self.load_button.setEnabled(False)
        button_layout.addWidget(self.load_button)
        
        self.save_button = QPushButton("保存当前参数为预设")
        self.save_button.clicked.connect(self.save_current_preset)
        button_layout.addWidget(self.save_button)
        
        self.delete_button = QPushButton("删除预设")
        self.delete_button.clicked.connect(self.delete_selected_preset)
        self.delete_button.setEnabled(False)
        button_layout.addWidget(self.delete_button)
        
        button_layout.addStretch()
        
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
    
    def load_presets(self):
        """加载预设列表"""
        try:
            presets = self.config.get("presets", {})
            
            self.preset_list.clear()
            for name in presets.keys():
                self.preset_list.addItem(name)
            
            self.logger.info(f"加载了 {len(presets)} 个预设")
            
        except Exception as e:
            self.logger.error(f"加载预设列表失败: {e}")
    
    def on_preset_selected(self, item):
        """预设选中"""
        if item:
            preset_name = item.text()
            self.load_button.setEnabled(True)
            self.delete_button.setEnabled(True)
            
            # 显示预设详情
            self.show_preset_detail(preset_name)
        else:
            self.load_button.setEnabled(False)
            self.delete_button.setEnabled(False)
            self.preset_detail_text.clear()
    
    def show_preset_detail(self, preset_name: str):
        """显示预设详情"""
        try:
            preset_data = self.config.get(f"presets.{preset_name}")
            if preset_data:
                # 格式化显示预设参数
                detail_text = f"预设名称: {preset_name}\n\n"
                detail_text += "主要参数:\n"
                
                if "fusion_type" in preset_data:
                    detail_text += f"• 融合类型: {preset_data['fusion_type']}\n"
                
                if "alpha" in preset_data:
                    detail_text += f"• 透明度: {preset_data['alpha']:.2f}\n"
                
                if "resize_mode" in preset_data:
                    detail_text += f"• 调整模式: {preset_data['resize_mode']}\n"
                
                if "text_overlay" in preset_data and preset_data["text_overlay"]["enabled"]:
                    detail_text += f"• 文字叠加: 启用\n"
                
                if "edge_detection" in preset_data and preset_data["edge_detection"]["enabled"]:
                    detail_text += f"• 边缘检测: {preset_data['edge_detection']['method']}\n"
                
                self.preset_detail_text.setPlainText(detail_text)
            
        except Exception as e:
            self.logger.error(f"显示预设详情失败: {e}")
            self.preset_detail_text.setPlainText("无法显示预设详情")
    
    def load_selected_preset(self):
        """加载选中的预设"""
        try:
            current_item = self.preset_list.currentItem()
            if current_item:
                preset_name = current_item.text()
                preset_data = self.config.get(f"presets.{preset_name}")
                
                if preset_data:
                    self.preset_loaded.emit(preset_data)
                    self.logger.info(f"加载预设: {preset_name}")
                    
                    QMessageBox.information(self, "成功", f"预设 '{preset_name}' 已加载")
                else:
                    QMessageBox.warning(self, "错误", f"预设 '{preset_name}' 不存在")
            
        except Exception as e:
            self.logger.error(f"加载预设失败: {e}")
            QMessageBox.critical(self, "错误", f"加载预设失败: {e}")
    
    def save_current_preset(self):
        """保存当前参数为预设"""
        try:
            preset_name = self.preset_name_edit.text().strip()
            if not preset_name:
                QMessageBox.warning(self, "警告", "请输入预设名称")
                return
            
            # 检查预设是否已存在
            existing_presets = self.config.get("presets", {})
            if preset_name in existing_presets:
                reply = QMessageBox.question(
                    self, "确认", 
                    f"预设 '{preset_name}' 已存在，是否覆盖？",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return
            
            # 发送信号请求保存当前参数
            self.save_preset_requested.emit(preset_name)
            
        except Exception as e:
            self.logger.error(f"保存预设失败: {e}")
            QMessageBox.critical(self, "错误", f"保存预设失败: {e}")
    
    def delete_selected_preset(self):
        """删除选中的预设"""
        try:
            current_item = self.preset_list.currentItem()
            if current_item:
                preset_name = current_item.text()
                
                reply = QMessageBox.question(
                    self, "确认删除", 
                    f"确定要删除预设 '{preset_name}' 吗？",
                    QMessageBox.Yes | QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    # 从配置中删除预设
                    presets = self.config.get("presets", {})
                    if preset_name in presets:
                        del presets[preset_name]
                        self.config.set("presets", presets)
                        self.config.save()
                        
                        # 刷新预设列表
                        self.load_presets()
                        
                        self.logger.info(f"删除预设: {preset_name}")
                        QMessageBox.information(self, "成功", f"预设 '{preset_name}' 已删除")
            
        except Exception as e:
            self.logger.error(f"删除预设失败: {e}")
            QMessageBox.critical(self, "错误", f"删除预设失败: {e}")
    
    def add_preset(self, name: str, params: Dict[str, Any]):
        """添加新预设"""
        try:
            presets = self.config.get("presets", {})
            presets[name] = params
            self.config.set("presets", presets)
            self.config.save()
            
            # 刷新预设列表
            self.load_presets()
            
            # 选中新添加的预设
            items = self.preset_list.findItems(name, Qt.MatchExactly)
            if items:
                self.preset_list.setCurrentItem(items[0])
                self.on_preset_selected(items[0])
            
            self.preset_name_edit.clear()
            self.logger.info(f"添加预设: {name}")
            QMessageBox.information(self, "成功", f"预设 '{name}' 已保存")
            
        except Exception as e:
            self.logger.error(f"添加预设失败: {e}")
            QMessageBox.critical(self, "错误", f"添加预设失败: {e}")
    
    # 添加信号定义
    save_preset_requested = pyqtSignal(str)


class DefaultPresets:
    """默认预设"""
    
    @staticmethod
    def get_default_presets() -> Dict[str, Dict[str, Any]]:
        """获取默认预设"""
        return {
            "基础插入": {
                "fusion_type": "插入融合 (Insertion)",
                "alpha": 1.0,
                "resize_mode": "fit",
                "insertion_mode": "直接插入",
                "frame_range": (0, 100),
                "text_overlay": {"enabled": False},
                "edge_detection": {"enabled": False},
                "histogram_processing": {"match": False, "equalize": False, "gamma": 1.0}
            },
            
            "透明叠加": {
                "fusion_type": "叠加融合 (Overlay)",
                "alpha": 0.7,
                "resize_mode": "fit",
                "overlay_mode": "正常",
                "overlay_position": {"x": 50, "y": 50, "width": 200, "height": 150, "moving": False},
                "text_overlay": {"enabled": False},
                "edge_detection": {"enabled": False}
            },
            
            "柔和混合": {
                "fusion_type": "混合融合 (Blend)",
                "alpha": 0.5,
                "resize_mode": "fit",
                "blend_mode": "线性",
                "feather_radius": 10,
                "text_overlay": {"enabled": False},
                "histogram_processing": {"match": True, "equalize": False, "gamma": 1.0}
            },
            
            "标题叠加": {
                "fusion_type": "叠加融合 (Overlay)",
                "alpha": 0.8,
                "resize_mode": "fit",
                "overlay_mode": "正常",
                "text_overlay": {
                    "enabled": True,
                    "content": "视频标题",
                    "style": "标题",
                    "font_size": 36,
                    "color": (255, 255, 0),
                    "alpha": 1.0,
                    "position": "上中",
                    "animation": False
                }
            }
        }
    
    @staticmethod
    def install_default_presets(config_manager: ConfigManager):
        """安装默认预设"""
        try:
            default_presets = DefaultPresets.get_default_presets()
            existing_presets = config_manager.get("presets", {})
            
            # 只添加不存在的默认预设
            for name, preset in default_presets.items():
                if name not in existing_presets:
                    existing_presets[name] = preset
            
            config_manager.set("presets", existing_presets)
            config_manager.save()
            
            return len(default_presets)
            
        except Exception as e:
            Logger.get_logger(__name__).error(f"安装默认预设失败: {e}")
            return 0
