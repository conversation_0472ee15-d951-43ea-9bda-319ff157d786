"""
批量处理对话框
Batch Processing Dialog
"""

import os
from typing import Dict, Any, List
from pathlib import Path

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                             QWidget, QLabel, QPushButton, QTableWidget, QTableWidgetItem,
                             QProgressBar, QTextEdit, QGroupBox, QLineEdit, QSpinBox,
                             QComboBox, QDoubleSpinBox, QFileDialog, QMessageBox,
                             QHeaderView, QAbstractItemView, QCheckBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont

from ..batch.batch_processor import BatchProcessor, BatchTask, BatchTaskStatus
from ..fusion.fusion_engine import FusionType
from ..utils.logger import Logger


class BatchProcessingThread(QThread):
    """批量处理线程"""
    
    progress_updated = pyqtSignal(str, float, str)  # task_id, progress, message
    task_completed = pyqtSignal(object)  # BatchTask
    batch_completed = pyqtSignal(dict)   # statistics
    
    def __init__(self, batch_processor: BatchProcessor):
        super().__init__()
        self.batch_processor = batch_processor
        
        # 设置回调函数
        self.batch_processor.set_progress_callback(self.on_progress_updated)
        self.batch_processor.set_task_completed_callback(self.on_task_completed)
        self.batch_processor.set_batch_completed_callback(self.on_batch_completed)
    
    def run(self):
        """运行批量处理"""
        self.batch_processor.start_batch_processing()
    
    def stop_processing(self):
        """停止处理"""
        self.batch_processor.stop_batch_processing()
    
    def on_progress_updated(self, task_id: str, progress: float, message: str):
        """进度更新回调"""
        self.progress_updated.emit(task_id, progress, message)
    
    def on_task_completed(self, task: BatchTask):
        """任务完成回调"""
        self.task_completed.emit(task)
    
    def on_batch_completed(self, stats: Dict[str, Any]):
        """批量完成回调"""
        self.batch_completed.emit(stats)


class BatchDialog(QDialog):
    """批量处理对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger.get_logger(__name__)
        
        # 批量处理器
        self.batch_processor = BatchProcessor()
        self.processing_thread: BatchProcessingThread = None
        
        # 初始化UI
        self.init_ui()
        
        # 定时器更新状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(1000)  # 每秒更新
        
        self.logger.info("批量处理对话框初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("批量处理")
        self.setModal(True)
        self.resize(800, 600)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 标签页
        tab_widget = QTabWidget()
        
        # 任务配置标签页
        config_tab = self.create_config_tab()
        tab_widget.addTab(config_tab, "任务配置")
        
        # 任务列表标签页
        task_list_tab = self.create_task_list_tab()
        tab_widget.addTab(task_list_tab, "任务列表")
        
        # 处理状态标签页
        status_tab = self.create_status_tab()
        tab_widget.addTab(status_tab, "处理状态")
        
        main_layout.addWidget(tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始批量处理")
        self.start_btn.clicked.connect(self.start_batch_processing)
        button_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止处理")
        self.stop_btn.clicked.connect(self.stop_batch_processing)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        button_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)
        
        main_layout.addLayout(button_layout)
    
    def create_config_tab(self) -> QWidget:
        """创建任务配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 文件夹批量配置组
        folder_group = QGroupBox("从文件夹批量创建任务")
        folder_layout = QVBoxLayout(folder_group)
        
        # A视频文件夹
        a_folder_layout = QHBoxLayout()
        a_folder_layout.addWidget(QLabel("A视频文件夹:"))
        self.a_folder_edit = QLineEdit()
        a_folder_layout.addWidget(self.a_folder_edit)
        
        a_folder_btn = QPushButton("浏览")
        a_folder_btn.clicked.connect(self.browse_a_folder)
        a_folder_layout.addWidget(a_folder_btn)
        
        folder_layout.addLayout(a_folder_layout)
        
        # B视频文件夹
        b_folder_layout = QHBoxLayout()
        b_folder_layout.addWidget(QLabel("B视频文件夹:"))
        self.b_folder_edit = QLineEdit()
        b_folder_layout.addWidget(self.b_folder_edit)
        
        b_folder_btn = QPushButton("浏览")
        b_folder_btn.clicked.connect(self.browse_b_folder)
        b_folder_layout.addWidget(b_folder_btn)
        
        folder_layout.addLayout(b_folder_layout)
        
        # 输出文件夹
        output_folder_layout = QHBoxLayout()
        output_folder_layout.addWidget(QLabel("输出文件夹:"))
        self.output_folder_edit = QLineEdit()
        output_folder_layout.addWidget(self.output_folder_edit)
        
        output_folder_btn = QPushButton("浏览")
        output_folder_btn.clicked.connect(self.browse_output_folder)
        output_folder_layout.addWidget(output_folder_btn)
        
        folder_layout.addLayout(output_folder_layout)
        
        # 融合参数
        params_group = QGroupBox("融合参数")
        params_layout = QVBoxLayout(params_group)
        
        # 融合类型
        fusion_type_layout = QHBoxLayout()
        fusion_type_layout.addWidget(QLabel("融合类型:"))
        self.fusion_type_combo = QComboBox()
        self.fusion_type_combo.addItems(["insertion", "overlay", "blend"])
        fusion_type_layout.addWidget(self.fusion_type_combo)
        fusion_type_layout.addStretch()
        params_layout.addLayout(fusion_type_layout)
        
        # 透明度
        alpha_layout = QHBoxLayout()
        alpha_layout.addWidget(QLabel("透明度:"))
        self.alpha_spin = QDoubleSpinBox()
        self.alpha_spin.setRange(0.0, 1.0)
        self.alpha_spin.setSingleStep(0.1)
        self.alpha_spin.setValue(0.5)
        alpha_layout.addWidget(self.alpha_spin)
        alpha_layout.addStretch()
        params_layout.addLayout(alpha_layout)
        
        # 调整模式
        resize_layout = QHBoxLayout()
        resize_layout.addWidget(QLabel("调整模式:"))
        self.resize_combo = QComboBox()
        self.resize_combo.addItems(["fit", "fill", "stretch"])
        resize_layout.addWidget(self.resize_combo)
        resize_layout.addStretch()
        params_layout.addLayout(resize_layout)
        
        # 输出帧率
        fps_layout = QHBoxLayout()
        fps_layout.addWidget(QLabel("输出帧率:"))
        self.fps_spin = QDoubleSpinBox()
        self.fps_spin.setRange(1.0, 60.0)
        self.fps_spin.setValue(30.0)
        fps_layout.addWidget(self.fps_spin)
        fps_layout.addStretch()
        params_layout.addLayout(fps_layout)
        
        folder_layout.addWidget(params_group)
        
        # 创建任务按钮
        create_btn = QPushButton("创建批量任务")
        create_btn.clicked.connect(self.create_batch_tasks)
        folder_layout.addWidget(create_btn)
        
        layout.addWidget(folder_group)
        
        # 配置文件组
        config_group = QGroupBox("配置文件")
        config_layout = QHBoxLayout(config_group)
        
        load_config_btn = QPushButton("加载配置")
        load_config_btn.clicked.connect(self.load_config)
        config_layout.addWidget(load_config_btn)
        
        save_config_btn = QPushButton("保存配置")
        save_config_btn.clicked.connect(self.save_config)
        config_layout.addWidget(save_config_btn)
        
        config_layout.addStretch()
        
        layout.addWidget(config_group)
        
        layout.addStretch()
        
        return widget
    
    def create_task_list_tab(self) -> QWidget:
        """创建任务列表标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 任务列表表格
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(6)
        self.task_table.setHorizontalHeaderLabels([
            "任务ID", "A视频", "B视频", "输出路径", "状态", "进度"
        ])
        
        # 设置表格属性
        header = self.task_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.task_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.task_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.task_table)
        
        # 任务操作按钮
        task_btn_layout = QHBoxLayout()
        
        clear_btn = QPushButton("清空任务")
        clear_btn.clicked.connect(self.clear_tasks)
        task_btn_layout.addWidget(clear_btn)
        
        remove_btn = QPushButton("删除选中")
        remove_btn.clicked.connect(self.remove_selected_tasks)
        task_btn_layout.addWidget(remove_btn)
        
        task_btn_layout.addStretch()
        
        refresh_btn = QPushButton("刷新列表")
        refresh_btn.clicked.connect(self.refresh_task_list)
        task_btn_layout.addWidget(refresh_btn)
        
        layout.addLayout(task_btn_layout)
        
        return widget
    
    def create_status_tab(self) -> QWidget:
        """创建处理状态标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 总体进度
        progress_group = QGroupBox("总体进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.overall_progress = QProgressBar()
        progress_layout.addWidget(self.overall_progress)
        
        self.progress_label = QLabel("等待开始...")
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_group)
        
        # 当前任务
        current_group = QGroupBox("当前任务")
        current_layout = QVBoxLayout(current_group)
        
        self.current_task_label = QLabel("无")
        current_layout.addWidget(self.current_task_label)
        
        self.current_progress = QProgressBar()
        current_layout.addWidget(self.current_progress)
        
        self.current_status_label = QLabel("等待开始...")
        current_layout.addWidget(self.current_status_label)
        
        layout.addWidget(current_group)
        
        # 统计信息
        stats_group = QGroupBox("统计信息")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(150)
        self.stats_text.setReadOnly(True)
        stats_layout.addWidget(self.stats_text)
        
        layout.addWidget(stats_group)
        
        # 日志
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        return widget
    
    def browse_a_folder(self):
        """浏览A视频文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择A视频文件夹")
        if folder:
            self.a_folder_edit.setText(folder)
    
    def browse_b_folder(self):
        """浏览B视频文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择B视频文件夹")
        if folder:
            self.b_folder_edit.setText(folder)
    
    def browse_output_folder(self):
        """浏览输出文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if folder:
            self.output_folder_edit.setText(folder)
    
    def create_batch_tasks(self):
        """创建批量任务"""
        try:
            # 获取文件夹路径
            a_folder = self.a_folder_edit.text().strip()
            b_folder = self.b_folder_edit.text().strip()
            output_folder = self.output_folder_edit.text().strip()
            
            if not a_folder or not b_folder or not output_folder:
                QMessageBox.warning(self, "警告", "请选择所有必需的文件夹")
                return
            
            # 获取融合参数
            fusion_params = {
                "fusion_type": self.fusion_type_combo.currentText(),
                "alpha": self.alpha_spin.value(),
                "resize_mode": self.resize_combo.currentText(),
                "output_fps": self.fps_spin.value()
            }
            
            # 创建批量任务
            count = self.batch_processor.create_batch_from_folders(
                a_folder, b_folder, output_folder, fusion_params
            )
            
            if count > 0:
                QMessageBox.information(self, "成功", f"成功创建了 {count} 个批量任务")
                self.refresh_task_list()
            else:
                QMessageBox.warning(self, "警告", "没有创建任何任务，请检查文件夹中是否有视频文件")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建批量任务失败:\n{str(e)}")
    
    def load_config(self):
        """加载配置文件"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "加载批量配置", "", "JSON文件 (*.json);;所有文件 (*)"
            )
            
            if file_path:
                count = self.batch_processor.add_tasks_from_config(file_path)
                if count > 0:
                    QMessageBox.information(self, "成功", f"成功加载了 {count} 个任务")
                    self.refresh_task_list()
                else:
                    QMessageBox.warning(self, "警告", "没有加载任何任务")
                    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载配置失败:\n{str(e)}")
    
    def save_config(self):
        """保存配置文件"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存批量配置", "", "JSON文件 (*.json);;所有文件 (*)"
            )
            
            if file_path:
                if self.batch_processor.export_batch_config(file_path):
                    QMessageBox.information(self, "成功", "配置文件保存成功")
                else:
                    QMessageBox.warning(self, "失败", "配置文件保存失败")
                    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置失败:\n{str(e)}")
    
    def refresh_task_list(self):
        """刷新任务列表"""
        try:
            tasks = self.batch_processor.get_all_tasks()
            
            self.task_table.setRowCount(len(tasks))
            
            for row, task in enumerate(tasks):
                # 任务ID
                self.task_table.setItem(row, 0, QTableWidgetItem(task.task_id))
                
                # A视频（只显示文件名）
                a_name = Path(task.video_a_path).name
                self.task_table.setItem(row, 1, QTableWidgetItem(a_name))
                
                # B视频（只显示文件名）
                b_name = Path(task.video_b_path).name
                self.task_table.setItem(row, 2, QTableWidgetItem(b_name))
                
                # 输出路径（只显示文件名）
                output_name = Path(task.output_path).name
                self.task_table.setItem(row, 3, QTableWidgetItem(output_name))
                
                # 状态
                status_text = {
                    BatchTaskStatus.PENDING: "等待中",
                    BatchTaskStatus.PROCESSING: "处理中",
                    BatchTaskStatus.COMPLETED: "已完成",
                    BatchTaskStatus.FAILED: "失败",
                    BatchTaskStatus.CANCELLED: "已取消"
                }.get(task.status, "未知")
                
                self.task_table.setItem(row, 4, QTableWidgetItem(status_text))
                
                # 进度
                progress_text = f"{task.progress:.1f}%"
                self.task_table.setItem(row, 5, QTableWidgetItem(progress_text))
                
        except Exception as e:
            self.logger.error(f"刷新任务列表失败: {e}")
    
    def clear_tasks(self):
        """清空任务"""
        try:
            if self.batch_processor.is_processing:
                QMessageBox.warning(self, "警告", "批量处理进行中，无法清空任务")
                return
            
            reply = QMessageBox.question(
                self, "确认", "确定要清空所有任务吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.batch_processor.clear_tasks()
                self.refresh_task_list()
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"清空任务失败:\n{str(e)}")
    
    def remove_selected_tasks(self):
        """删除选中的任务"""
        # TODO: 实现删除选中任务的功能
        QMessageBox.information(self, "提示", "删除选中任务功能开发中")
    
    def start_batch_processing(self):
        """开始批量处理"""
        try:
            if not self.batch_processor.get_all_tasks():
                QMessageBox.warning(self, "警告", "没有待处理的任务")
                return
            
            # 创建处理线程
            self.processing_thread = BatchProcessingThread(self.batch_processor)
            self.processing_thread.progress_updated.connect(self.on_progress_updated)
            self.processing_thread.task_completed.connect(self.on_task_completed)
            self.processing_thread.batch_completed.connect(self.on_batch_completed)
            
            # 启动线程
            self.processing_thread.start()
            
            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            
            self.log_message("批量处理已开始")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动批量处理失败:\n{str(e)}")
    
    def stop_batch_processing(self):
        """停止批量处理"""
        try:
            if self.processing_thread:
                self.processing_thread.stop_processing()
            
            # 更新UI状态
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            
            self.log_message("批量处理已停止")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止批量处理失败:\n{str(e)}")
    
    def on_progress_updated(self, task_id: str, progress: float, message: str):
        """进度更新处理"""
        self.current_task_label.setText(f"任务: {task_id}")
        self.current_progress.setValue(int(progress))
        self.current_status_label.setText(message)
        
        self.log_message(f"[{task_id}] {message} ({progress:.1f}%)")
    
    def on_task_completed(self, task: BatchTask):
        """任务完成处理"""
        status_text = "成功" if task.status == BatchTaskStatus.COMPLETED else "失败"
        self.log_message(f"任务 {task.task_id} {status_text}")
        
        # 刷新任务列表
        self.refresh_task_list()
    
    def on_batch_completed(self, stats: Dict[str, Any]):
        """批量完成处理"""
        self.log_message(f"批量处理完成 - 总任务: {stats['total_tasks']}, "
                        f"成功: {stats['completed_tasks']}, 失败: {stats['failed_tasks']}")
        
        # 更新UI状态
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        # 显示完成对话框
        QMessageBox.information(
            self, "完成", 
            f"批量处理完成！\n\n"
            f"总任务数: {stats['total_tasks']}\n"
            f"成功: {stats['completed_tasks']}\n"
            f"失败: {stats['failed_tasks']}\n"
            f"成功率: {stats['success_rate']:.1f}%\n"
            f"总耗时: {stats['total_time']:.1f}秒"
        )
    
    def update_status(self):
        """更新状态显示"""
        try:
            stats = self.batch_processor.get_statistics()
            
            # 更新总体进度
            if stats['total_tasks'] > 0:
                completed = stats['completed_tasks'] + stats['failed_tasks']
                progress = (completed / stats['total_tasks']) * 100
                self.overall_progress.setValue(int(progress))
                
                self.progress_label.setText(
                    f"进度: {completed}/{stats['total_tasks']} "
                    f"(成功: {stats['completed_tasks']}, 失败: {stats['failed_tasks']})"
                )
            
            # 更新统计信息
            stats_text = f"""总任务数: {stats['total_tasks']}
已完成: {stats['completed_tasks']}
失败: {stats['failed_tasks']}
等待中: {stats['pending_tasks']}
处理状态: {'进行中' if stats['is_processing'] else '空闲'}
当前任务: {stats['current_task'] or '无'}"""
            
            self.stats_text.setText(stats_text)
            
        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")
    
    def log_message(self, message: str):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_msg = f"[{timestamp}] {message}"
        
        self.log_text.append(formatted_msg)
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.batch_processor.is_processing:
            reply = QMessageBox.question(
                self, "确认关闭", 
                "批量处理正在进行中，确定要关闭吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                event.ignore()
                return
            
            # 停止批量处理
            self.stop_batch_processing()
        
        event.accept()
