"""
直方图匹配器
Histogram Matcher
"""

import cv2
import numpy as np
from typing import Tuple, Optional, List
from skimage import exposure

from ..utils.logger import Logger


class HistogramMatcher:
    """直方图匹配器类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.logger.info("直方图匹配器初始化完成")
    
    def match_histograms(self, source: np.ndarray, 
                        reference: np.ndarray,
                        multichannel: bool = True) -> np.ndarray:
        """直方图匹配
        
        Args:
            source: 源图像
            reference: 参考图像
            multichannel: 是否为多通道图像
        
        Returns:
            匹配后的图像
        """
        try:
            # 使用scikit-image的直方图匹配
            matched = exposure.match_histograms(source, reference, multichannel=multichannel)
            
            # 确保数据类型正确
            matched = np.clip(matched, 0, 255).astype(np.uint8)
            
            self.logger.debug("直方图匹配完成")
            return matched
            
        except Exception as e:
            self.logger.error(f"直方图匹配失败: {e}")
            return source.copy()
    
    def match_histograms_opencv(self, source: np.ndarray, 
                               reference: np.ndarray) -> np.ndarray:
        """使用OpenCV实现直方图匹配
        
        Args:
            source: 源图像
            reference: 参考图像
        
        Returns:
            匹配后的图像
        """
        try:
            if len(source.shape) == 3 and len(reference.shape) == 3:
                # 彩色图像，分别处理每个通道
                matched = np.zeros_like(source)
                for i in range(3):
                    matched[:, :, i] = self._match_single_channel(
                        source[:, :, i], reference[:, :, i]
                    )
            else:
                # 灰度图像
                if len(source.shape) == 3:
                    source = cv2.cvtColor(source, cv2.COLOR_BGR2GRAY)
                if len(reference.shape) == 3:
                    reference = cv2.cvtColor(reference, cv2.COLOR_BGR2GRAY)
                
                matched = self._match_single_channel(source, reference)
            
            self.logger.debug("OpenCV直方图匹配完成")
            return matched
            
        except Exception as e:
            self.logger.error(f"OpenCV直方图匹配失败: {e}")
            return source.copy()
    
    def _match_single_channel(self, source: np.ndarray, 
                             reference: np.ndarray) -> np.ndarray:
        """单通道直方图匹配
        
        Args:
            source: 源图像（单通道）
            reference: 参考图像（单通道）
        
        Returns:
            匹配后的图像
        """
        try:
            # 计算直方图
            source_hist = cv2.calcHist([source], [0], None, [256], [0, 256])
            reference_hist = cv2.calcHist([reference], [0], None, [256], [0, 256])
            
            # 计算累积分布函数
            source_cdf = source_hist.cumsum()
            reference_cdf = reference_hist.cumsum()
            
            # 归一化CDF
            source_cdf = source_cdf / source_cdf[-1]
            reference_cdf = reference_cdf / reference_cdf[-1]
            
            # 创建查找表
            lookup_table = np.zeros(256, dtype=np.uint8)
            
            for i in range(256):
                # 找到最接近的参考CDF值
                diff = np.abs(reference_cdf - source_cdf[i])
                lookup_table[i] = np.argmin(diff)
            
            # 应用查找表
            matched = cv2.LUT(source, lookup_table)
            
            return matched
            
        except Exception as e:
            self.logger.error(f"单通道直方图匹配失败: {e}")
            return source.copy()
    
    def calculate_histogram(self, image: np.ndarray, 
                           bins: int = 256) -> List[np.ndarray]:
        """计算图像直方图
        
        Args:
            image: 输入图像
            bins: 直方图bins数量
        
        Returns:
            直方图列表（每个通道一个）
        """
        try:
            histograms = []
            
            if len(image.shape) == 3:
                # 彩色图像，计算每个通道的直方图
                for i in range(3):
                    hist = cv2.calcHist([image], [i], None, [bins], [0, 256])
                    histograms.append(hist.flatten())
            else:
                # 灰度图像
                hist = cv2.calcHist([image], [0], None, [bins], [0, 256])
                histograms.append(hist.flatten())
            
            self.logger.debug(f"计算直方图完成，bins: {bins}")
            return histograms
            
        except Exception as e:
            self.logger.error(f"计算直方图失败: {e}")
            return []
    
    def equalize_histogram(self, image: np.ndarray) -> np.ndarray:
        """直方图均衡化
        
        Args:
            image: 输入图像
        
        Returns:
            均衡化后的图像
        """
        try:
            if len(image.shape) == 3:
                # 彩色图像，转换到YUV空间进行均衡化
                yuv = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)
                yuv[:, :, 0] = cv2.equalizeHist(yuv[:, :, 0])
                equalized = cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)
            else:
                # 灰度图像
                equalized = cv2.equalizeHist(image)
            
            self.logger.debug("直方图均衡化完成")
            return equalized
            
        except Exception as e:
            self.logger.error(f"直方图均衡化失败: {e}")
            return image.copy()
    
    def adaptive_equalize_histogram(self, image: np.ndarray,
                                   clip_limit: float = 2.0,
                                   tile_grid_size: Tuple[int, int] = (8, 8)) -> np.ndarray:
        """自适应直方图均衡化（CLAHE）
        
        Args:
            image: 输入图像
            clip_limit: 对比度限制阈值
            tile_grid_size: 瓦片网格大小
        
        Returns:
            均衡化后的图像
        """
        try:
            # 创建CLAHE对象
            clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
            
            if len(image.shape) == 3:
                # 彩色图像，转换到LAB空间进行均衡化
                lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                lab[:, :, 0] = clahe.apply(lab[:, :, 0])
                equalized = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            else:
                # 灰度图像
                equalized = clahe.apply(image)
            
            self.logger.debug(f"自适应直方图均衡化完成，clip_limit: {clip_limit}")
            return equalized
            
        except Exception as e:
            self.logger.error(f"自适应直方图均衡化失败: {e}")
            return image.copy()
    
    def calculate_histogram_similarity(self, hist1: np.ndarray, 
                                     hist2: np.ndarray,
                                     method: int = cv2.HISTCMP_CORREL) -> float:
        """计算直方图相似度
        
        Args:
            hist1: 第一个直方图
            hist2: 第二个直方图
            method: 比较方法
        
        Returns:
            相似度值
        """
        try:
            similarity = cv2.compareHist(hist1, hist2, method)
            
            self.logger.debug(f"直方图相似度: {similarity:.4f}")
            return similarity
            
        except Exception as e:
            self.logger.error(f"计算直方图相似度失败: {e}")
            return 0.0
    
    def adjust_brightness_contrast(self, image: np.ndarray,
                                  brightness: int = 0,
                                  contrast: float = 1.0) -> np.ndarray:
        """调整亮度和对比度
        
        Args:
            image: 输入图像
            brightness: 亮度调整值 (-100 到 100)
            contrast: 对比度调整值 (0.0 到 3.0)
        
        Returns:
            调整后的图像
        """
        try:
            # 应用亮度和对比度调整
            adjusted = cv2.convertScaleAbs(image, alpha=contrast, beta=brightness)
            
            self.logger.debug(f"亮度对比度调整完成，亮度: {brightness}, 对比度: {contrast}")
            return adjusted
            
        except Exception as e:
            self.logger.error(f"亮度对比度调整失败: {e}")
            return image.copy()
    
    def adjust_gamma(self, image: np.ndarray, gamma: float = 1.0) -> np.ndarray:
        """Gamma校正
        
        Args:
            image: 输入图像
            gamma: Gamma值
        
        Returns:
            校正后的图像
        """
        try:
            # 构建查找表
            inv_gamma = 1.0 / gamma
            table = np.array([((i / 255.0) ** inv_gamma) * 255 
                             for i in np.arange(0, 256)]).astype("uint8")
            
            # 应用Gamma校正
            corrected = cv2.LUT(image, table)
            
            self.logger.debug(f"Gamma校正完成，gamma: {gamma}")
            return corrected
            
        except Exception as e:
            self.logger.error(f"Gamma校正失败: {e}")
            return image.copy()
    
    def create_histogram_visualization(self, image: np.ndarray,
                                     size: Tuple[int, int] = (400, 300)) -> np.ndarray:
        """创建直方图可视化图像
        
        Args:
            image: 输入图像
            size: 可视化图像大小
        
        Returns:
            直方图可视化图像
        """
        try:
            width, height = size
            
            # 计算直方图
            if len(image.shape) == 3:
                # 彩色图像
                colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255)]  # BGR
                hist_image = np.zeros((height, width, 3), dtype=np.uint8)
                
                for i, color in enumerate(colors):
                    hist = cv2.calcHist([image], [i], None, [256], [0, 256])
                    hist = cv2.normalize(hist, hist, 0, height, cv2.NORM_MINMAX)
                    
                    for j in range(256):
                        cv2.line(hist_image, 
                                (int(j * width / 256), height),
                                (int(j * width / 256), height - int(hist[j])),
                                color, 1)
            else:
                # 灰度图像
                hist = cv2.calcHist([image], [0], None, [256], [0, 256])
                hist = cv2.normalize(hist, hist, 0, height, cv2.NORM_MINMAX)
                
                hist_image = np.zeros((height, width, 3), dtype=np.uint8)
                
                for i in range(256):
                    cv2.line(hist_image,
                            (int(i * width / 256), height),
                            (int(i * width / 256), height - int(hist[i])),
                            (255, 255, 255), 1)
            
            self.logger.debug("直方图可视化创建完成")
            return hist_image
            
        except Exception as e:
            self.logger.error(f"创建直方图可视化失败: {e}")
            return np.zeros((size[1], size[0], 3), dtype=np.uint8)
