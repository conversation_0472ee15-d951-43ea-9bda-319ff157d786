"""
图像处理器
Image Processor
"""

import cv2
import numpy as np
from typing import Tuple, Optional, Dict, Any, List
from enum import Enum

from ..utils.logger import Logger
from .edge_detector import EdgeDetector, EdgeDetectionMethod
from .histogram_matcher import HistogramMatcher


class FilterType(Enum):
    """滤镜类型枚举"""
    GAUSSIAN_BLUR = "gaussian_blur"
    MOTION_BLUR = "motion_blur"
    MEDIAN_BLUR = "median_blur"
    BILATERAL_FILTER = "bilateral_filter"
    SHARPEN = "sharpen"
    EMBOSS = "emboss"
    NOISE_REDUCTION = "noise_reduction"


class ImageProcessor:
    """图像处理器主类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.edge_detector = EdgeDetector()
        self.histogram_matcher = HistogramMatcher()
        self.logger.info("图像处理器初始化完成")
    
    def resize_image(self, image: np.ndarray, 
                    target_size: Tuple[int, int],
                    interpolation: int = cv2.INTER_AREA) -> np.ndarray:
        """调整图像大小
        
        Args:
            image: 输入图像
            target_size: 目标尺寸 (width, height)
            interpolation: 插值方法
        
        Returns:
            调整后的图像
        """
        try:
            resized = cv2.resize(image, target_size, interpolation=interpolation)
            
            self.logger.debug(f"图像大小调整: {image.shape[:2]} -> {target_size}")
            return resized
            
        except Exception as e:
            self.logger.error(f"图像大小调整失败: {e}")
            return image.copy()
    
    def crop_image(self, image: np.ndarray, 
                  x: int, y: int, width: int, height: int) -> np.ndarray:
        """裁剪图像
        
        Args:
            image: 输入图像
            x: 起始x坐标
            y: 起始y坐标
            width: 裁剪宽度
            height: 裁剪高度
        
        Returns:
            裁剪后的图像
        """
        try:
            h, w = image.shape[:2]
            
            # 确保裁剪区域在有效范围内
            x = max(0, min(x, w - 1))
            y = max(0, min(y, h - 1))
            width = min(width, w - x)
            height = min(height, h - y)
            
            cropped = image[y:y+height, x:x+width]
            
            self.logger.debug(f"图像裁剪: ({x}, {y}, {width}, {height})")
            return cropped
            
        except Exception as e:
            self.logger.error(f"图像裁剪失败: {e}")
            return image.copy()
    
    def rotate_image(self, image: np.ndarray, 
                    angle: float, 
                    center: Optional[Tuple[int, int]] = None) -> np.ndarray:
        """旋转图像
        
        Args:
            image: 输入图像
            angle: 旋转角度（度）
            center: 旋转中心，None表示图像中心
        
        Returns:
            旋转后的图像
        """
        try:
            h, w = image.shape[:2]
            
            if center is None:
                center = (w // 2, h // 2)
            
            # 计算旋转矩阵
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            
            # 计算新的边界框大小
            cos = np.abs(rotation_matrix[0, 0])
            sin = np.abs(rotation_matrix[0, 1])
            new_w = int((h * sin) + (w * cos))
            new_h = int((h * cos) + (w * sin))
            
            # 调整旋转矩阵的平移部分
            rotation_matrix[0, 2] += (new_w / 2) - center[0]
            rotation_matrix[1, 2] += (new_h / 2) - center[1]
            
            # 执行旋转
            rotated = cv2.warpAffine(image, rotation_matrix, (new_w, new_h))
            
            self.logger.debug(f"图像旋转: {angle}度")
            return rotated
            
        except Exception as e:
            self.logger.error(f"图像旋转失败: {e}")
            return image.copy()
    
    def flip_image(self, image: np.ndarray, flip_code: int) -> np.ndarray:
        """翻转图像
        
        Args:
            image: 输入图像
            flip_code: 翻转代码 (0=垂直, 1=水平, -1=水平垂直)
        
        Returns:
            翻转后的图像
        """
        try:
            flipped = cv2.flip(image, flip_code)
            
            flip_type = {0: "垂直", 1: "水平", -1: "水平垂直"}
            self.logger.debug(f"图像翻转: {flip_type.get(flip_code, '未知')}")
            return flipped
            
        except Exception as e:
            self.logger.error(f"图像翻转失败: {e}")
            return image.copy()
    
    def apply_filter(self, image: np.ndarray, 
                    filter_type: FilterType, 
                    **kwargs) -> np.ndarray:
        """应用滤镜
        
        Args:
            image: 输入图像
            filter_type: 滤镜类型
            **kwargs: 滤镜参数
        
        Returns:
            滤镜处理后的图像
        """
        try:
            if filter_type == FilterType.GAUSSIAN_BLUR:
                return self._apply_gaussian_blur(image, **kwargs)
            elif filter_type == FilterType.MOTION_BLUR:
                return self._apply_motion_blur(image, **kwargs)
            elif filter_type == FilterType.MEDIAN_BLUR:
                return self._apply_median_blur(image, **kwargs)
            elif filter_type == FilterType.BILATERAL_FILTER:
                return self._apply_bilateral_filter(image, **kwargs)
            elif filter_type == FilterType.SHARPEN:
                return self._apply_sharpen(image, **kwargs)
            elif filter_type == FilterType.EMBOSS:
                return self._apply_emboss(image, **kwargs)
            elif filter_type == FilterType.NOISE_REDUCTION:
                return self._apply_noise_reduction(image, **kwargs)
            else:
                self.logger.error(f"不支持的滤镜类型: {filter_type}")
                return image.copy()
                
        except Exception as e:
            self.logger.error(f"应用滤镜失败: {e}")
            return image.copy()
    
    def _apply_gaussian_blur(self, image: np.ndarray, 
                           kernel_size: int = 5, 
                           sigma: float = 0) -> np.ndarray:
        """应用高斯模糊"""
        if kernel_size % 2 == 0:
            kernel_size += 1
        
        blurred = cv2.GaussianBlur(image, (kernel_size, kernel_size), sigma)
        self.logger.debug(f"高斯模糊: 核大小={kernel_size}, sigma={sigma}")
        return blurred
    
    def _apply_motion_blur(self, image: np.ndarray, 
                          size: int = 15, 
                          angle: float = 0) -> np.ndarray:
        """应用运动模糊"""
        # 创建运动模糊核
        kernel = np.zeros((size, size))
        kernel[int((size-1)/2), :] = np.ones(size)
        kernel = kernel / size
        
        # 旋转核
        if angle != 0:
            center = (size // 2, size // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            kernel = cv2.warpAffine(kernel, rotation_matrix, (size, size))
        
        blurred = cv2.filter2D(image, -1, kernel)
        self.logger.debug(f"运动模糊: 大小={size}, 角度={angle}")
        return blurred
    
    def _apply_median_blur(self, image: np.ndarray, kernel_size: int = 5) -> np.ndarray:
        """应用中值模糊"""
        if kernel_size % 2 == 0:
            kernel_size += 1
        
        blurred = cv2.medianBlur(image, kernel_size)
        self.logger.debug(f"中值模糊: 核大小={kernel_size}")
        return blurred
    
    def _apply_bilateral_filter(self, image: np.ndarray, 
                               d: int = 9, 
                               sigma_color: float = 75, 
                               sigma_space: float = 75) -> np.ndarray:
        """应用双边滤波"""
        filtered = cv2.bilateralFilter(image, d, sigma_color, sigma_space)
        self.logger.debug(f"双边滤波: d={d}, sigma_color={sigma_color}, sigma_space={sigma_space}")
        return filtered
    
    def _apply_sharpen(self, image: np.ndarray, strength: float = 1.0) -> np.ndarray:
        """应用锐化滤镜"""
        # 锐化核
        kernel = np.array([[-1, -1, -1],
                          [-1, 9, -1],
                          [-1, -1, -1]]) * strength
        kernel[1, 1] = 8 * strength + 1
        
        sharpened = cv2.filter2D(image, -1, kernel)
        sharpened = np.clip(sharpened, 0, 255).astype(np.uint8)
        
        self.logger.debug(f"锐化: 强度={strength}")
        return sharpened
    
    def _apply_emboss(self, image: np.ndarray) -> np.ndarray:
        """应用浮雕效果"""
        # 浮雕核
        kernel = np.array([[-2, -1, 0],
                          [-1, 1, 1],
                          [0, 1, 2]])
        
        embossed = cv2.filter2D(image, -1, kernel)
        embossed = np.clip(embossed + 128, 0, 255).astype(np.uint8)
        
        self.logger.debug("浮雕效果")
        return embossed
    
    def _apply_noise_reduction(self, image: np.ndarray, 
                              h: float = 10, 
                              template_window_size: int = 7,
                              search_window_size: int = 21) -> np.ndarray:
        """应用降噪"""
        if len(image.shape) == 3:
            denoised = cv2.fastNlMeansDenoisingColored(
                image, None, h, h, template_window_size, search_window_size
            )
        else:
            denoised = cv2.fastNlMeansDenoising(
                image, None, h, template_window_size, search_window_size
            )
        
        self.logger.debug(f"降噪: h={h}")
        return denoised
    
    def create_mask(self, image: np.ndarray, 
                   mask_type: str = "circle",
                   **kwargs) -> np.ndarray:
        """创建掩码
        
        Args:
            image: 输入图像
            mask_type: 掩码类型 ("circle", "rectangle", "ellipse", "custom")
            **kwargs: 掩码参数
        
        Returns:
            掩码图像
        """
        try:
            h, w = image.shape[:2]
            mask = np.zeros((h, w), dtype=np.uint8)
            
            if mask_type == "circle":
                center = kwargs.get("center", (w//2, h//2))
                radius = kwargs.get("radius", min(w, h)//4)
                cv2.circle(mask, center, radius, 255, -1)
                
            elif mask_type == "rectangle":
                x = kwargs.get("x", w//4)
                y = kwargs.get("y", h//4)
                width = kwargs.get("width", w//2)
                height = kwargs.get("height", h//2)
                cv2.rectangle(mask, (x, y), (x+width, y+height), 255, -1)
                
            elif mask_type == "ellipse":
                center = kwargs.get("center", (w//2, h//2))
                axes = kwargs.get("axes", (w//4, h//4))
                angle = kwargs.get("angle", 0)
                cv2.ellipse(mask, center, axes, angle, 0, 360, 255, -1)
                
            self.logger.debug(f"创建掩码: {mask_type}")
            return mask
            
        except Exception as e:
            self.logger.error(f"创建掩码失败: {e}")
            return np.zeros((image.shape[0], image.shape[1]), dtype=np.uint8)
    
    def apply_mask(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """应用掩码
        
        Args:
            image: 输入图像
            mask: 掩码图像
        
        Returns:
            应用掩码后的图像
        """
        try:
            if len(image.shape) == 3:
                # 彩色图像
                masked = cv2.bitwise_and(image, image, mask=mask)
            else:
                # 灰度图像
                masked = cv2.bitwise_and(image, mask)
            
            self.logger.debug("应用掩码完成")
            return masked
            
        except Exception as e:
            self.logger.error(f"应用掩码失败: {e}")
            return image.copy()
    
    def blend_images(self, image1: np.ndarray, 
                    image2: np.ndarray, 
                    alpha: float = 0.5) -> np.ndarray:
        """混合两个图像
        
        Args:
            image1: 第一个图像
            image2: 第二个图像
            alpha: 混合权重
        
        Returns:
            混合后的图像
        """
        try:
            # 确保图像尺寸相同
            if image1.shape != image2.shape:
                image2 = cv2.resize(image2, (image1.shape[1], image1.shape[0]))
            
            blended = cv2.addWeighted(image1, alpha, image2, 1-alpha, 0)
            
            self.logger.debug(f"图像混合: alpha={alpha}")
            return blended
            
        except Exception as e:
            self.logger.error(f"图像混合失败: {e}")
            return image1.copy()
    
    def get_image_statistics(self, image: np.ndarray) -> Dict[str, Any]:
        """获取图像统计信息
        
        Args:
            image: 输入图像
        
        Returns:
            统计信息字典
        """
        try:
            stats = {
                'shape': image.shape,
                'dtype': str(image.dtype),
                'mean': np.mean(image),
                'std': np.std(image),
                'min': np.min(image),
                'max': np.max(image)
            }
            
            if len(image.shape) == 3:
                # 彩色图像，计算每个通道的统计信息
                for i, channel in enumerate(['B', 'G', 'R']):
                    stats[f'{channel}_mean'] = np.mean(image[:, :, i])
                    stats[f'{channel}_std'] = np.std(image[:, :, i])
            
            self.logger.debug("图像统计信息计算完成")
            return stats
            
        except Exception as e:
            self.logger.error(f"计算图像统计信息失败: {e}")
            return {}
