"""
边缘检测器
Edge Detector
"""

import cv2
import numpy as np
from typing import Tuple, Optional, Dict, Any
from enum import Enum

from ..utils.logger import Logger


class EdgeDetectionMethod(Enum):
    """边缘检测方法枚举"""
    CANNY = "canny"
    SOBEL = "sobel"
    LAPLACIAN = "laplacian"
    SCHARR = "scharr"


class EdgeDetector:
    """边缘检测器类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.logger.info("边缘检测器初始化完成")
    
    def detect_edges_canny(self, image: np.ndarray, 
                          low_threshold: int = 50, 
                          high_threshold: int = 150,
                          aperture_size: int = 3,
                          l2_gradient: bool = False) -> np.ndarray:
        """Canny边缘检测
        
        Args:
            image: 输入图像
            low_threshold: 低阈值
            high_threshold: 高阈值
            aperture_size: Sobel算子的孔径大小
            l2_gradient: 是否使用L2梯度
        
        Returns:
            边缘检测结果（二值图像）
        """
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 应用高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Canny边缘检测
            edges = cv2.Canny(blurred, low_threshold, high_threshold, 
                             apertureSize=aperture_size, L2gradient=l2_gradient)
            
            self.logger.debug(f"Canny边缘检测完成，阈值: {low_threshold}-{high_threshold}")
            return edges
            
        except Exception as e:
            self.logger.error(f"Canny边缘检测失败: {e}")
            return np.zeros_like(image[:, :, 0] if len(image.shape) == 3 else image)
    
    def detect_edges_sobel(self, image: np.ndarray, 
                          ksize: int = 3, 
                          scale: float = 1.0,
                          delta: float = 0.0) -> np.ndarray:
        """Sobel边缘检测
        
        Args:
            image: 输入图像
            ksize: Sobel核大小
            scale: 缩放因子
            delta: 偏移值
        
        Returns:
            边缘检测结果
        """
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 计算X和Y方向的梯度
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=ksize, scale=scale, delta=delta)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=ksize, scale=scale, delta=delta)
            
            # 计算梯度幅值
            magnitude = np.sqrt(grad_x**2 + grad_y**2)
            
            # 归一化到0-255范围
            magnitude = np.clip(magnitude, 0, 255).astype(np.uint8)
            
            self.logger.debug(f"Sobel边缘检测完成，核大小: {ksize}")
            return magnitude
            
        except Exception as e:
            self.logger.error(f"Sobel边缘检测失败: {e}")
            return np.zeros_like(image[:, :, 0] if len(image.shape) == 3 else image)
    
    def detect_edges_laplacian(self, image: np.ndarray, 
                              ksize: int = 1,
                              scale: float = 1.0,
                              delta: float = 0.0) -> np.ndarray:
        """Laplacian边缘检测
        
        Args:
            image: 输入图像
            ksize: 核大小
            scale: 缩放因子
            delta: 偏移值
        
        Returns:
            边缘检测结果
        """
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 应用高斯模糊
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # Laplacian边缘检测
            laplacian = cv2.Laplacian(blurred, cv2.CV_64F, ksize=ksize, scale=scale, delta=delta)
            
            # 取绝对值并归一化
            laplacian = np.absolute(laplacian)
            laplacian = np.clip(laplacian, 0, 255).astype(np.uint8)
            
            self.logger.debug(f"Laplacian边缘检测完成，核大小: {ksize}")
            return laplacian
            
        except Exception as e:
            self.logger.error(f"Laplacian边缘检测失败: {e}")
            return np.zeros_like(image[:, :, 0] if len(image.shape) == 3 else image)
    
    def detect_edges_scharr(self, image: np.ndarray,
                           scale: float = 1.0,
                           delta: float = 0.0) -> np.ndarray:
        """Scharr边缘检测
        
        Args:
            image: 输入图像
            scale: 缩放因子
            delta: 偏移值
        
        Returns:
            边缘检测结果
        """
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 计算X和Y方向的Scharr梯度
            grad_x = cv2.Scharr(gray, cv2.CV_64F, 1, 0, scale=scale, delta=delta)
            grad_y = cv2.Scharr(gray, cv2.CV_64F, 0, 1, scale=scale, delta=delta)
            
            # 计算梯度幅值
            magnitude = np.sqrt(grad_x**2 + grad_y**2)
            
            # 归一化到0-255范围
            magnitude = np.clip(magnitude, 0, 255).astype(np.uint8)
            
            self.logger.debug("Scharr边缘检测完成")
            return magnitude
            
        except Exception as e:
            self.logger.error(f"Scharr边缘检测失败: {e}")
            return np.zeros_like(image[:, :, 0] if len(image.shape) == 3 else image)
    
    def detect_edges(self, image: np.ndarray, 
                    method: EdgeDetectionMethod = EdgeDetectionMethod.CANNY,
                    **kwargs) -> np.ndarray:
        """通用边缘检测接口
        
        Args:
            image: 输入图像
            method: 边缘检测方法
            **kwargs: 方法特定参数
        
        Returns:
            边缘检测结果
        """
        try:
            if method == EdgeDetectionMethod.CANNY:
                return self.detect_edges_canny(image, **kwargs)
            elif method == EdgeDetectionMethod.SOBEL:
                return self.detect_edges_sobel(image, **kwargs)
            elif method == EdgeDetectionMethod.LAPLACIAN:
                return self.detect_edges_laplacian(image, **kwargs)
            elif method == EdgeDetectionMethod.SCHARR:
                return self.detect_edges_scharr(image, **kwargs)
            else:
                self.logger.error(f"不支持的边缘检测方法: {method}")
                return np.zeros_like(image[:, :, 0] if len(image.shape) == 3 else image)
                
        except Exception as e:
            self.logger.error(f"边缘检测失败: {e}")
            return np.zeros_like(image[:, :, 0] if len(image.shape) == 3 else image)
    
    def create_binary_edge_mask(self, edges: np.ndarray, 
                               threshold: int = 127) -> np.ndarray:
        """创建二值边缘掩码
        
        Args:
            edges: 边缘检测结果
            threshold: 二值化阈值
        
        Returns:
            二值掩码
        """
        try:
            # 二值化
            _, binary_mask = cv2.threshold(edges, threshold, 255, cv2.THRESH_BINARY)
            
            self.logger.debug(f"创建二值边缘掩码，阈值: {threshold}")
            return binary_mask
            
        except Exception as e:
            self.logger.error(f"创建二值边缘掩码失败: {e}")
            return np.zeros_like(edges)
    
    def dilate_edges(self, edges: np.ndarray, 
                    kernel_size: int = 3, 
                    iterations: int = 1) -> np.ndarray:
        """膨胀边缘
        
        Args:
            edges: 边缘图像
            kernel_size: 膨胀核大小
            iterations: 膨胀迭代次数
        
        Returns:
            膨胀后的边缘图像
        """
        try:
            kernel = np.ones((kernel_size, kernel_size), np.uint8)
            dilated = cv2.dilate(edges, kernel, iterations=iterations)
            
            self.logger.debug(f"边缘膨胀完成，核大小: {kernel_size}, 迭代: {iterations}")
            return dilated
            
        except Exception as e:
            self.logger.error(f"边缘膨胀失败: {e}")
            return edges
    
    def erode_edges(self, edges: np.ndarray, 
                   kernel_size: int = 3, 
                   iterations: int = 1) -> np.ndarray:
        """腐蚀边缘
        
        Args:
            edges: 边缘图像
            kernel_size: 腐蚀核大小
            iterations: 腐蚀迭代次数
        
        Returns:
            腐蚀后的边缘图像
        """
        try:
            kernel = np.ones((kernel_size, kernel_size), np.uint8)
            eroded = cv2.erode(edges, kernel, iterations=iterations)
            
            self.logger.debug(f"边缘腐蚀完成，核大小: {kernel_size}, 迭代: {iterations}")
            return eroded
            
        except Exception as e:
            self.logger.error(f"边缘腐蚀失败: {e}")
            return edges
    
    def get_edge_statistics(self, edges: np.ndarray) -> Dict[str, Any]:
        """获取边缘统计信息
        
        Args:
            edges: 边缘图像
        
        Returns:
            统计信息字典
        """
        try:
            total_pixels = edges.shape[0] * edges.shape[1]
            edge_pixels = np.count_nonzero(edges)
            edge_ratio = edge_pixels / total_pixels
            
            stats = {
                'total_pixels': total_pixels,
                'edge_pixels': edge_pixels,
                'edge_ratio': edge_ratio,
                'mean_intensity': np.mean(edges),
                'max_intensity': np.max(edges),
                'min_intensity': np.min(edges)
            }
            
            self.logger.debug(f"边缘统计: 边缘像素比例 {edge_ratio:.3f}")
            return stats
            
        except Exception as e:
            self.logger.error(f"计算边缘统计信息失败: {e}")
            return {}
    
    def create_colored_edges(self, edges: np.ndarray, 
                           color: Tuple[int, int, int] = (0, 255, 0)) -> np.ndarray:
        """创建彩色边缘图像
        
        Args:
            edges: 边缘图像（灰度）
            color: 边缘颜色 (B, G, R)
        
        Returns:
            彩色边缘图像
        """
        try:
            # 创建三通道图像
            colored_edges = np.zeros((edges.shape[0], edges.shape[1], 3), dtype=np.uint8)
            
            # 设置边缘颜色
            mask = edges > 0
            colored_edges[mask] = color
            
            self.logger.debug(f"创建彩色边缘图像，颜色: {color}")
            return colored_edges
            
        except Exception as e:
            self.logger.error(f"创建彩色边缘图像失败: {e}")
            return np.zeros((edges.shape[0], edges.shape[1], 3), dtype=np.uint8)
