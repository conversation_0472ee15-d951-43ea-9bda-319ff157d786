"""
文字叠加功能
Text Overlay
"""

import cv2
import numpy as np
from typing import Tuple, Optional, Dict, Any, List
from enum import Enum

from ..utils.logger import Logger


class TextPosition(Enum):
    """文字位置枚举"""
    TOP_LEFT = "top_left"
    TOP_CENTER = "top_center"
    TOP_RIGHT = "top_right"
    CENTER_LEFT = "center_left"
    CENTER = "center"
    CENTER_RIGHT = "center_right"
    BOTTOM_LEFT = "bottom_left"
    BOTTOM_CENTER = "bottom_center"
    BOTTOM_RIGHT = "bottom_right"
    CUSTOM = "custom"


class TextStyle:
    """文字样式类"""
    
    def __init__(self):
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 1.0
        self.color = (255, 255, 255)  # 白色
        self.thickness = 2
        self.line_type = cv2.LINE_AA
        self.background_color: Optional[Tuple[int, int, int]] = None
        self.border_color: Optional[Tuple[int, int, int]] = None
        self.border_thickness = 1
        self.shadow_offset: Optional[Tuple[int, int]] = None
        self.shadow_color = (0, 0, 0)  # 黑色阴影
        self.opacity = 1.0  # 不透明度
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'font': self.font,
            'font_scale': self.font_scale,
            'color': self.color,
            'thickness': self.thickness,
            'line_type': self.line_type,
            'background_color': self.background_color,
            'border_color': self.border_color,
            'border_thickness': self.border_thickness,
            'shadow_offset': self.shadow_offset,
            'shadow_color': self.shadow_color,
            'opacity': self.opacity
        }


class TextOverlay:
    """文字叠加类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.logger.info("文字叠加功能初始化完成")
    
    def add_text(self, image: np.ndarray, 
                text: str,
                position: Tuple[int, int],
                style: TextStyle) -> np.ndarray:
        """在图像上添加文字
        
        Args:
            image: 输入图像
            text: 要添加的文字
            position: 文字位置 (x, y)
            style: 文字样式
        
        Returns:
            添加文字后的图像
        """
        try:
            result = image.copy()
            
            # 如果有阴影，先绘制阴影
            if style.shadow_offset:
                shadow_pos = (position[0] + style.shadow_offset[0], 
                             position[1] + style.shadow_offset[1])
                cv2.putText(result, text, shadow_pos, style.font, style.font_scale,
                           style.shadow_color, style.thickness, style.line_type)
            
            # 如果有背景色，绘制背景矩形
            if style.background_color:
                text_size = cv2.getTextSize(text, style.font, style.font_scale, style.thickness)[0]
                bg_x1 = position[0] - 5
                bg_y1 = position[1] - text_size[1] - 5
                bg_x2 = position[0] + text_size[0] + 5
                bg_y2 = position[1] + 5
                
                cv2.rectangle(result, (bg_x1, bg_y1), (bg_x2, bg_y2), 
                             style.background_color, -1)
                
                # 如果有边框，绘制边框
                if style.border_color:
                    cv2.rectangle(result, (bg_x1, bg_y1), (bg_x2, bg_y2),
                                 style.border_color, style.border_thickness)
            
            # 绘制主文字
            if style.opacity < 1.0:
                # 创建文字图层用于透明度处理
                text_layer = np.zeros_like(result)
                cv2.putText(text_layer, text, position, style.font, style.font_scale,
                           style.color, style.thickness, style.line_type)
                
                # 应用透明度
                result = cv2.addWeighted(result, 1.0, text_layer, style.opacity, 0)
            else:
                cv2.putText(result, text, position, style.font, style.font_scale,
                           style.color, style.thickness, style.line_type)
            
            self.logger.debug(f"添加文字: '{text}' 位置: {position}")
            return result
            
        except Exception as e:
            self.logger.error(f"添加文字失败: {e}")
            return image.copy()
    
    def add_text_with_position(self, image: np.ndarray,
                              text: str,
                              position_type: TextPosition,
                              style: TextStyle,
                              custom_position: Optional[Tuple[int, int]] = None,
                              margin: int = 10) -> np.ndarray:
        """使用预定义位置添加文字
        
        Args:
            image: 输入图像
            text: 要添加的文字
            position_type: 位置类型
            style: 文字样式
            custom_position: 自定义位置（当position_type为CUSTOM时使用）
            margin: 边距
        
        Returns:
            添加文字后的图像
        """
        try:
            h, w = image.shape[:2]
            
            # 计算文字尺寸
            text_size = cv2.getTextSize(text, style.font, style.font_scale, style.thickness)[0]
            text_width, text_height = text_size
            
            # 根据位置类型计算坐标
            if position_type == TextPosition.TOP_LEFT:
                x, y = margin, margin + text_height
            elif position_type == TextPosition.TOP_CENTER:
                x, y = (w - text_width) // 2, margin + text_height
            elif position_type == TextPosition.TOP_RIGHT:
                x, y = w - text_width - margin, margin + text_height
            elif position_type == TextPosition.CENTER_LEFT:
                x, y = margin, (h + text_height) // 2
            elif position_type == TextPosition.CENTER:
                x, y = (w - text_width) // 2, (h + text_height) // 2
            elif position_type == TextPosition.CENTER_RIGHT:
                x, y = w - text_width - margin, (h + text_height) // 2
            elif position_type == TextPosition.BOTTOM_LEFT:
                x, y = margin, h - margin
            elif position_type == TextPosition.BOTTOM_CENTER:
                x, y = (w - text_width) // 2, h - margin
            elif position_type == TextPosition.BOTTOM_RIGHT:
                x, y = w - text_width - margin, h - margin
            elif position_type == TextPosition.CUSTOM:
                if custom_position:
                    x, y = custom_position
                else:
                    x, y = (w - text_width) // 2, (h + text_height) // 2
            else:
                x, y = (w - text_width) // 2, (h + text_height) // 2
            
            return self.add_text(image, text, (x, y), style)
            
        except Exception as e:
            self.logger.error(f"使用预定义位置添加文字失败: {e}")
            return image.copy()
    
    def add_multiline_text(self, image: np.ndarray,
                          lines: List[str],
                          start_position: Tuple[int, int],
                          style: TextStyle,
                          line_spacing: int = 10) -> np.ndarray:
        """添加多行文字
        
        Args:
            image: 输入图像
            lines: 文字行列表
            start_position: 起始位置
            style: 文字样式
            line_spacing: 行间距
        
        Returns:
            添加文字后的图像
        """
        try:
            result = image.copy()
            x, y = start_position
            
            for i, line in enumerate(lines):
                if line.strip():  # 跳过空行
                    text_size = cv2.getTextSize(line, style.font, style.font_scale, style.thickness)[0]
                    line_y = y + i * (text_size[1] + line_spacing)
                    result = self.add_text(result, line, (x, line_y), style)
            
            self.logger.debug(f"添加多行文字: {len(lines)}行")
            return result
            
        except Exception as e:
            self.logger.error(f"添加多行文字失败: {e}")
            return image.copy()
    
    def create_text_mask(self, image_shape: Tuple[int, int],
                        text: str,
                        position: Tuple[int, int],
                        style: TextStyle) -> np.ndarray:
        """创建文字掩码
        
        Args:
            image_shape: 图像尺寸 (height, width)
            text: 文字内容
            position: 文字位置
            style: 文字样式
        
        Returns:
            文字掩码
        """
        try:
            mask = np.zeros(image_shape, dtype=np.uint8)
            
            cv2.putText(mask, text, position, style.font, style.font_scale,
                       255, style.thickness, style.line_type)
            
            self.logger.debug(f"创建文字掩码: '{text}'")
            return mask
            
        except Exception as e:
            self.logger.error(f"创建文字掩码失败: {e}")
            return np.zeros(image_shape, dtype=np.uint8)
    
    def add_animated_text(self, images: List[np.ndarray],
                         text: str,
                         start_position: Tuple[int, int],
                         end_position: Tuple[int, int],
                         style: TextStyle) -> List[np.ndarray]:
        """添加动画文字（位置渐变）
        
        Args:
            images: 图像序列
            text: 文字内容
            start_position: 起始位置
            end_position: 结束位置
            style: 文字样式
        
        Returns:
            添加动画文字后的图像序列
        """
        try:
            result_images = []
            num_frames = len(images)
            
            for i, image in enumerate(images):
                # 计算当前帧的文字位置
                progress = i / (num_frames - 1) if num_frames > 1 else 0
                current_x = int(start_position[0] + (end_position[0] - start_position[0]) * progress)
                current_y = int(start_position[1] + (end_position[1] - start_position[1]) * progress)
                
                result_image = self.add_text(image, text, (current_x, current_y), style)
                result_images.append(result_image)
            
            self.logger.debug(f"添加动画文字: {num_frames}帧")
            return result_images
            
        except Exception as e:
            self.logger.error(f"添加动画文字失败: {e}")
            return images.copy()
    
    def add_fade_text(self, images: List[np.ndarray],
                     text: str,
                     position: Tuple[int, int],
                     style: TextStyle,
                     fade_in_frames: int = 10,
                     fade_out_frames: int = 10) -> List[np.ndarray]:
        """添加淡入淡出文字
        
        Args:
            images: 图像序列
            text: 文字内容
            position: 文字位置
            style: 文字样式
            fade_in_frames: 淡入帧数
            fade_out_frames: 淡出帧数
        
        Returns:
            添加淡入淡出文字后的图像序列
        """
        try:
            result_images = []
            num_frames = len(images)
            
            for i, image in enumerate(images):
                current_style = TextStyle()
                current_style.__dict__.update(style.__dict__)
                
                # 计算当前帧的透明度
                if i < fade_in_frames:
                    # 淡入阶段
                    current_style.opacity = (i + 1) / fade_in_frames * style.opacity
                elif i >= num_frames - fade_out_frames:
                    # 淡出阶段
                    fade_progress = (num_frames - i - 1) / fade_out_frames
                    current_style.opacity = fade_progress * style.opacity
                else:
                    # 完全显示阶段
                    current_style.opacity = style.opacity
                
                result_image = self.add_text(image, text, position, current_style)
                result_images.append(result_image)
            
            self.logger.debug(f"添加淡入淡出文字: {num_frames}帧")
            return result_images
            
        except Exception as e:
            self.logger.error(f"添加淡入淡出文字失败: {e}")
            return images.copy()
    
    def get_text_bounds(self, text: str, style: TextStyle) -> Tuple[int, int]:
        """获取文字边界框尺寸
        
        Args:
            text: 文字内容
            style: 文字样式
        
        Returns:
            文字尺寸 (width, height)
        """
        try:
            text_size = cv2.getTextSize(text, style.font, style.font_scale, style.thickness)[0]
            return text_size
            
        except Exception as e:
            self.logger.error(f"获取文字边界框失败: {e}")
            return (0, 0)
    
    def create_text_style_preset(self, preset_name: str) -> TextStyle:
        """创建文字样式预设
        
        Args:
            preset_name: 预设名称
        
        Returns:
            文字样式对象
        """
        style = TextStyle()
        
        if preset_name == "default":
            # 默认样式
            pass
        elif preset_name == "title":
            # 标题样式
            style.font_scale = 2.0
            style.thickness = 3
            style.color = (255, 255, 0)  # 黄色
            style.shadow_offset = (2, 2)
        elif preset_name == "subtitle":
            # 副标题样式
            style.font_scale = 1.5
            style.thickness = 2
            style.color = (255, 255, 255)  # 白色
            style.background_color = (0, 0, 0)  # 黑色背景
        elif preset_name == "watermark":
            # 水印样式
            style.font_scale = 0.8
            style.thickness = 1
            style.color = (200, 200, 200)  # 浅灰色
            style.opacity = 0.7
        elif preset_name == "warning":
            # 警告样式
            style.font_scale = 1.2
            style.thickness = 2
            style.color = (0, 0, 255)  # 红色
            style.background_color = (0, 255, 255)  # 黄色背景
            style.border_color = (0, 0, 255)  # 红色边框
            style.border_thickness = 2
        
        self.logger.debug(f"创建文字样式预设: {preset_name}")
        return style
