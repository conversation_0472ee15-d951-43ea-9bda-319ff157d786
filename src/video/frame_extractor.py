"""
帧提取器
Frame Extractor
"""

import cv2
import numpy as np
from typing import List, Optional, Tuple, Generator
from pathlib import Path

from ..utils.logger import Logger
from .video_loader import VideoLoader


class FrameExtractor:
    """帧提取器类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
    
    def extract_key_frames(self, video_loader: VideoLoader, 
                          threshold: float = 0.3, max_frames: int = 100) -> List[Tuple[int, np.ndarray]]:
        """提取关键帧"""
        try:
            if not video_loader.is_loaded():
                self.logger.error("视频未加载")
                return []
            
            video_info = video_loader.get_current_info()
            if not video_info:
                return []
            
            key_frames = []
            prev_frame = None
            frame_count = 0
            
            self.logger.info("开始提取关键帧...")
            
            # 遍历所有帧
            for frame_number, frame in video_loader.get_frame_iterator():
                if prev_frame is None:
                    # 第一帧总是关键帧
                    key_frames.append((frame_number, frame.copy()))
                    prev_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    frame_count += 1
                    continue
                
                # 转换为灰度图
                gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # 计算帧间差异
                diff = cv2.absdiff(prev_frame, gray_frame)
                diff_score = np.mean(diff) / 255.0
                
                # 如果差异超过阈值，认为是关键帧
                if diff_score > threshold:
                    key_frames.append((frame_number, frame.copy()))
                    frame_count += 1
                    
                    if frame_count >= max_frames:
                        break
                
                prev_frame = gray_frame
                
                # 每处理100帧输出一次进度
                if frame_number % 100 == 0:
                    self.logger.debug(f"已处理 {frame_number} 帧，提取了 {frame_count} 个关键帧")
            
            self.logger.info(f"关键帧提取完成，共提取 {len(key_frames)} 个关键帧")
            return key_frames
            
        except Exception as e:
            self.logger.error(f"提取关键帧时发生错误: {e}")
            return []
    
    def extract_frames_by_interval(self, video_loader: VideoLoader, 
                                  interval: int = 30) -> List[Tuple[int, np.ndarray]]:
        """按间隔提取帧"""
        try:
            if not video_loader.is_loaded():
                self.logger.error("视频未加载")
                return []
            
            frames = []
            
            for frame_number, frame in video_loader.get_frame_iterator():
                if frame_number % interval == 0:
                    frames.append((frame_number, frame.copy()))
            
            self.logger.info(f"按间隔提取完成，共提取 {len(frames)} 帧")
            return frames
            
        except Exception as e:
            self.logger.error(f"按间隔提取帧时发生错误: {e}")
            return []
    
    def extract_frames_by_time(self, video_loader: VideoLoader, 
                              time_points: List[float]) -> List[Tuple[float, np.ndarray]]:
        """按时间点提取帧"""
        try:
            if not video_loader.is_loaded():
                self.logger.error("视频未加载")
                return []
            
            video_info = video_loader.get_current_info()
            if not video_info:
                return []
            
            frames = []
            
            for time_point in time_points:
                if time_point < 0 or time_point > video_info.duration:
                    self.logger.warning(f"时间点超出范围: {time_point}")
                    continue
                
                frame = video_loader.get_frame_at_time(time_point)
                if frame is not None:
                    frames.append((time_point, frame.copy()))
            
            self.logger.info(f"按时间点提取完成，共提取 {len(frames)} 帧")
            return frames
            
        except Exception as e:
            self.logger.error(f"按时间点提取帧时发生错误: {e}")
            return []
    
    def extract_scene_changes(self, video_loader: VideoLoader, 
                             threshold: float = 0.5) -> List[Tuple[int, np.ndarray]]:
        """提取场景变化帧"""
        try:
            if not video_loader.is_loaded():
                self.logger.error("视频未加载")
                return []
            
            scene_frames = []
            prev_hist = None
            
            self.logger.info("开始检测场景变化...")
            
            for frame_number, frame in video_loader.get_frame_iterator():
                # 计算直方图
                hist = cv2.calcHist([frame], [0, 1, 2], None, [50, 50, 50], [0, 256, 0, 256, 0, 256])
                hist = cv2.normalize(hist, hist).flatten()
                
                if prev_hist is not None:
                    # 计算直方图相关性
                    correlation = cv2.compareHist(prev_hist, hist, cv2.HISTCMP_CORREL)
                    
                    # 如果相关性低于阈值，认为是场景变化
                    if correlation < threshold:
                        scene_frames.append((frame_number, frame.copy()))
                        self.logger.debug(f"检测到场景变化，帧号: {frame_number}, 相关性: {correlation:.3f}")
                
                prev_hist = hist
            
            self.logger.info(f"场景变化检测完成，共检测到 {len(scene_frames)} 个场景变化")
            return scene_frames
            
        except Exception as e:
            self.logger.error(f"检测场景变化时发生错误: {e}")
            return []
    
    def extract_motion_frames(self, video_loader: VideoLoader, 
                             motion_threshold: float = 0.1) -> List[Tuple[int, np.ndarray]]:
        """提取运动帧"""
        try:
            if not video_loader.is_loaded():
                self.logger.error("视频未加载")
                return []
            
            motion_frames = []
            prev_gray = None
            
            self.logger.info("开始检测运动帧...")
            
            for frame_number, frame in video_loader.get_frame_iterator():
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                if prev_gray is not None:
                    # 计算光流
                    flow = cv2.calcOpticalFlowPyrLK(prev_gray, gray, None, None)
                    
                    # 计算运动强度
                    if flow[0] is not None:
                        motion_magnitude = np.mean(np.sqrt(flow[0][:, :, 0]**2 + flow[0][:, :, 1]**2))
                        
                        if motion_magnitude > motion_threshold:
                            motion_frames.append((frame_number, frame.copy()))
                
                prev_gray = gray
            
            self.logger.info(f"运动帧检测完成，共检测到 {len(motion_frames)} 个运动帧")
            return motion_frames
            
        except Exception as e:
            self.logger.error(f"检测运动帧时发生错误: {e}")
            return []
    
    def save_frames(self, frames: List[Tuple[int, np.ndarray]], 
                   output_dir: str, prefix: str = "frame") -> List[str]:
        """保存帧到文件"""
        try:
            from ..utils.file_utils import FileUtils
            
            # 确保输出目录存在
            FileUtils.ensure_directory(output_dir)
            
            saved_files = []
            
            for frame_number, frame in frames:
                filename = f"{prefix}_{frame_number:06d}.jpg"
                filepath = Path(output_dir) / filename
                
                if cv2.imwrite(str(filepath), frame):
                    saved_files.append(str(filepath))
                else:
                    self.logger.warning(f"保存帧失败: {filepath}")
            
            self.logger.info(f"帧保存完成，共保存 {len(saved_files)} 个文件")
            return saved_files
            
        except Exception as e:
            self.logger.error(f"保存帧时发生错误: {e}")
            return []
    
    def create_thumbnail_grid(self, frames: List[Tuple[int, np.ndarray]], 
                             grid_size: Tuple[int, int] = (4, 4),
                             thumbnail_size: Tuple[int, int] = (160, 120)) -> Optional[np.ndarray]:
        """创建缩略图网格"""
        try:
            rows, cols = grid_size
            thumb_h, thumb_w = thumbnail_size
            
            # 创建网格画布
            grid_h = rows * thumb_h
            grid_w = cols * thumb_w
            grid = np.zeros((grid_h, grid_w, 3), dtype=np.uint8)
            
            # 填充缩略图
            for i, (frame_number, frame) in enumerate(frames[:rows * cols]):
                row = i // cols
                col = i % cols
                
                # 调整帧大小
                thumbnail = cv2.resize(frame, (thumb_w, thumb_h))
                
                # 放置到网格中
                y_start = row * thumb_h
                y_end = y_start + thumb_h
                x_start = col * thumb_w
                x_end = x_start + thumb_w
                
                grid[y_start:y_end, x_start:x_end] = thumbnail
                
                # 添加帧号标签
                cv2.putText(grid, f"#{frame_number}", 
                           (x_start + 5, y_start + 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return grid
            
        except Exception as e:
            self.logger.error(f"创建缩略图网格时发生错误: {e}")
            return None
    
    def get_frame_statistics(self, frames: List[Tuple[int, np.ndarray]]) -> dict:
        """获取帧统计信息"""
        try:
            if not frames:
                return {}
            
            frame_numbers = [fn for fn, _ in frames]
            
            stats = {
                'total_frames': len(frames),
                'first_frame': min(frame_numbers),
                'last_frame': max(frame_numbers),
                'frame_range': max(frame_numbers) - min(frame_numbers),
                'average_interval': 0
            }
            
            # 计算平均间隔
            if len(frame_numbers) > 1:
                intervals = [frame_numbers[i+1] - frame_numbers[i] 
                           for i in range(len(frame_numbers)-1)]
                stats['average_interval'] = np.mean(intervals)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"计算帧统计信息时发生错误: {e}")
            return {}
