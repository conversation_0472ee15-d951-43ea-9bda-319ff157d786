"""
视频处理器
Video Processor
"""

import cv2
import numpy as np
from typing import Optional, List, Tuple, Dict, Any

from ..utils.logger import Logger
from .video_loader import VideoLoader, VideoInfo


class VideoProcessor:
    """视频处理器类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
    
    def resize_frame(self, frame: np.ndarray, target_size: Tuple[int, int], 
                    keep_aspect_ratio: bool = True) -> np.ndarray:
        """调整帧大小"""
        try:
            h, w = frame.shape[:2]
            target_w, target_h = target_size
            
            if keep_aspect_ratio:
                # 保持宽高比
                scale = min(target_w / w, target_h / h)
                new_w = int(w * scale)
                new_h = int(h * scale)
                
                # 调整大小
                resized = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_AREA)
                
                # 创建目标大小的画布
                result = np.zeros((target_h, target_w, frame.shape[2]), dtype=frame.dtype)
                
                # 计算居中位置
                y_offset = (target_h - new_h) // 2
                x_offset = (target_w - new_w) // 2
                
                # 将调整后的图像放置在画布中心
                result[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
                
                return result
            else:
                # 直接拉伸到目标大小
                return cv2.resize(frame, target_size, interpolation=cv2.INTER_AREA)
                
        except Exception as e:
            self.logger.error(f"调整帧大小时发生错误: {e}")
            return frame
    
    def crop_frame(self, frame: np.ndarray, x: int, y: int, width: int, height: int) -> np.ndarray:
        """裁剪帧"""
        try:
            h, w = frame.shape[:2]
            
            # 确保裁剪区域在有效范围内
            x = max(0, min(x, w - 1))
            y = max(0, min(y, h - 1))
            width = min(width, w - x)
            height = min(height, h - y)
            
            return frame[y:y+height, x:x+width]
            
        except Exception as e:
            self.logger.error(f"裁剪帧时发生错误: {e}")
            return frame
    
    def rotate_frame(self, frame: np.ndarray, angle: float) -> np.ndarray:
        """旋转帧"""
        try:
            h, w = frame.shape[:2]
            center = (w // 2, h // 2)
            
            # 计算旋转矩阵
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            
            # 计算新的边界框大小
            cos = np.abs(rotation_matrix[0, 0])
            sin = np.abs(rotation_matrix[0, 1])
            new_w = int((h * sin) + (w * cos))
            new_h = int((h * cos) + (w * sin))
            
            # 调整旋转矩阵的平移部分
            rotation_matrix[0, 2] += (new_w / 2) - center[0]
            rotation_matrix[1, 2] += (new_h / 2) - center[1]
            
            # 执行旋转
            rotated = cv2.warpAffine(frame, rotation_matrix, (new_w, new_h))
            
            return rotated
            
        except Exception as e:
            self.logger.error(f"旋转帧时发生错误: {e}")
            return frame
    
    def flip_frame(self, frame: np.ndarray, flip_code: int) -> np.ndarray:
        """翻转帧
        flip_code: 0=垂直翻转, 1=水平翻转, -1=水平垂直翻转
        """
        try:
            return cv2.flip(frame, flip_code)
        except Exception as e:
            self.logger.error(f"翻转帧时发生错误: {e}")
            return frame
    
    def adjust_brightness_contrast(self, frame: np.ndarray, brightness: float = 0, 
                                 contrast: float = 1.0) -> np.ndarray:
        """调整亮度和对比度"""
        try:
            # 应用亮度和对比度调整
            adjusted = cv2.convertScaleAbs(frame, alpha=contrast, beta=brightness)
            return adjusted
            
        except Exception as e:
            self.logger.error(f"调整亮度对比度时发生错误: {e}")
            return frame
    
    def apply_gaussian_blur(self, frame: np.ndarray, kernel_size: int = 5, 
                           sigma: float = 0) -> np.ndarray:
        """应用高斯模糊"""
        try:
            # 确保kernel_size为奇数
            if kernel_size % 2 == 0:
                kernel_size += 1
            
            blurred = cv2.GaussianBlur(frame, (kernel_size, kernel_size), sigma)
            return blurred
            
        except Exception as e:
            self.logger.error(f"应用高斯模糊时发生错误: {e}")
            return frame
    
    def convert_color_space(self, frame: np.ndarray, conversion_code: int) -> np.ndarray:
        """转换颜色空间"""
        try:
            converted = cv2.cvtColor(frame, conversion_code)
            return converted
            
        except Exception as e:
            self.logger.error(f"转换颜色空间时发生错误: {e}")
            return frame
    
    def extract_frames(self, video_loader: VideoLoader, output_dir: str, 
                      frame_interval: int = 1, max_frames: Optional[int] = None) -> List[str]:
        """提取视频帧到文件"""
        try:
            from pathlib import Path
            from ..utils.file_utils import FileUtils
            
            if not video_loader.is_loaded():
                self.logger.error("视频未加载")
                return []
            
            # 确保输出目录存在
            FileUtils.ensure_directory(output_dir)
            
            video_info = video_loader.get_current_info()
            if not video_info:
                return []
            
            extracted_files = []
            frame_count = 0
            
            # 使用帧迭代器提取帧
            for frame_number, frame in video_loader.get_frame_iterator():
                # 检查间隔
                if frame_number % frame_interval != 0:
                    continue
                
                # 检查最大帧数限制
                if max_frames and frame_count >= max_frames:
                    break
                
                # 生成文件名
                filename = f"frame_{frame_number:06d}.jpg"
                filepath = Path(output_dir) / filename
                
                # 保存帧
                if cv2.imwrite(str(filepath), frame):
                    extracted_files.append(str(filepath))
                    frame_count += 1
                    
                    if frame_count % 100 == 0:
                        self.logger.info(f"已提取 {frame_count} 帧")
            
            self.logger.info(f"帧提取完成，共提取 {len(extracted_files)} 帧")
            return extracted_files
            
        except Exception as e:
            self.logger.error(f"提取帧时发生错误: {e}")
            return []
    
    def create_video_from_frames(self, frame_paths: List[str], output_path: str, 
                               fps: float = 30.0, codec: str = 'mp4v') -> bool:
        """从帧序列创建视频"""
        try:
            if not frame_paths:
                self.logger.error("帧路径列表为空")
                return False
            
            # 读取第一帧以获取尺寸
            first_frame = cv2.imread(frame_paths[0])
            if first_frame is None:
                self.logger.error(f"无法读取第一帧: {frame_paths[0]}")
                return False
            
            h, w = first_frame.shape[:2]
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*codec)
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (w, h))
            
            if not video_writer.isOpened():
                self.logger.error(f"无法创建视频写入器: {output_path}")
                return False
            
            # 写入所有帧
            for i, frame_path in enumerate(frame_paths):
                frame = cv2.imread(frame_path)
                if frame is None:
                    self.logger.warning(f"跳过无效帧: {frame_path}")
                    continue
                
                # 确保帧尺寸一致
                if frame.shape[:2] != (h, w):
                    frame = cv2.resize(frame, (w, h))
                
                video_writer.write(frame)
                
                if (i + 1) % 100 == 0:
                    self.logger.info(f"已写入 {i + 1}/{len(frame_paths)} 帧")
            
            video_writer.release()
            self.logger.info(f"视频创建完成: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建视频时发生错误: {e}")
            return False
    
    def get_frame_difference(self, frame1: np.ndarray, frame2: np.ndarray) -> np.ndarray:
        """计算两帧之间的差异"""
        try:
            # 转换为灰度图
            if len(frame1.shape) == 3:
                gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
            else:
                gray1 = frame1
                
            if len(frame2.shape) == 3:
                gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
            else:
                gray2 = frame2
            
            # 计算绝对差值
            diff = cv2.absdiff(gray1, gray2)
            
            return diff
            
        except Exception as e:
            self.logger.error(f"计算帧差异时发生错误: {e}")
            return np.zeros_like(frame1[:, :, 0] if len(frame1.shape) == 3 else frame1)
    
    def detect_motion(self, frame1: np.ndarray, frame2: np.ndarray, 
                     threshold: int = 30) -> Tuple[bool, np.ndarray]:
        """检测运动"""
        try:
            # 计算帧差异
            diff = self.get_frame_difference(frame1, frame2)
            
            # 应用阈值
            _, thresh = cv2.threshold(diff, threshold, 255, cv2.THRESH_BINARY)
            
            # 计算运动像素数量
            motion_pixels = cv2.countNonZero(thresh)
            total_pixels = thresh.shape[0] * thresh.shape[1]
            motion_ratio = motion_pixels / total_pixels
            
            # 判断是否有显著运动（运动像素超过5%）
            has_motion = motion_ratio > 0.05
            
            return has_motion, thresh
            
        except Exception as e:
            self.logger.error(f"检测运动时发生错误: {e}")
            return False, np.zeros_like(frame1[:, :, 0] if len(frame1.shape) == 3 else frame1)
