"""
视频加载器
Video Loader
"""

import cv2
import numpy as np
from typing import Optional, Tu<PERSON>, Dict, Any
from pathlib import Path

from ..utils.logger import Logger
from ..utils.file_utils import FileUtils


class VideoInfo:
    """视频信息类"""
    
    def __init__(self):
        self.file_path: str = ""
        self.width: int = 0
        self.height: int = 0
        self.fps: float = 0.0
        self.frame_count: int = 0
        self.duration: float = 0.0
        self.codec: str = ""
        self.file_size: float = 0.0
        self.is_valid: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'file_path': self.file_path,
            'width': self.width,
            'height': self.height,
            'fps': self.fps,
            'frame_count': self.frame_count,
            'duration': self.duration,
            'codec': self.codec,
            'file_size_mb': self.file_size,
            'is_valid': self.is_valid
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        if not self.is_valid:
            return "无效视频"
        
        return (f"视频信息:\n"
                f"  文件: {Path(self.file_path).name}\n"
                f"  分辨率: {self.width}x{self.height}\n"
                f"  帧率: {self.fps:.2f} FPS\n"
                f"  帧数: {self.frame_count}\n"
                f"  时长: {self.duration:.2f}秒\n"
                f"  编码: {self.codec}\n"
                f"  大小: {self.file_size:.2f} MB")


class VideoLoader:
    """视频加载器类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.current_video: Optional[cv2.VideoCapture] = None
        self.video_info: Optional[VideoInfo] = None
    
    def load_video(self, file_path: str) -> Optional[VideoInfo]:
        """加载视频文件"""
        try:
            # 检查文件是否存在
            if not Path(file_path).exists():
                self.logger.error(f"视频文件不存在: {file_path}")
                return None
            
            # 检查是否为视频文件
            if not FileUtils.is_video_file(file_path):
                self.logger.error(f"不支持的视频格式: {file_path}")
                return None
            
            # 释放之前的视频
            self.release()
            
            # 打开视频文件
            self.current_video = cv2.VideoCapture(file_path)
            
            if not self.current_video.isOpened():
                self.logger.error(f"无法打开视频文件: {file_path}")
                return None
            
            # 获取视频信息
            self.video_info = self._extract_video_info(file_path)
            
            if self.video_info.is_valid:
                self.logger.info(f"视频加载成功: {file_path}")
                self.logger.info(f"视频信息: {self.video_info}")
            else:
                self.logger.error(f"视频信息提取失败: {file_path}")
                self.release()
                return None
            
            return self.video_info
            
        except Exception as e:
            self.logger.error(f"加载视频时发生错误: {e}")
            self.release()
            return None
    
    def _extract_video_info(self, file_path: str) -> VideoInfo:
        """提取视频信息"""
        info = VideoInfo()
        info.file_path = file_path
        
        try:
            if self.current_video is None:
                return info
            
            # 获取基本信息
            info.width = int(self.current_video.get(cv2.CAP_PROP_FRAME_WIDTH))
            info.height = int(self.current_video.get(cv2.CAP_PROP_FRAME_HEIGHT))
            info.fps = self.current_video.get(cv2.CAP_PROP_FPS)
            info.frame_count = int(self.current_video.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 计算时长
            if info.fps > 0:
                info.duration = info.frame_count / info.fps
            
            # 获取编码信息
            fourcc = int(self.current_video.get(cv2.CAP_PROP_FOURCC))
            info.codec = "".join([chr((fourcc >> 8 * i) & 0xFF) for i in range(4)])
            
            # 获取文件大小
            info.file_size = FileUtils.get_file_size_mb(file_path)
            
            # 验证信息有效性
            info.is_valid = (info.width > 0 and info.height > 0 and 
                           info.fps > 0 and info.frame_count > 0)
            
        except Exception as e:
            self.logger.error(f"提取视频信息时发生错误: {e}")
            info.is_valid = False
        
        return info
    
    def get_frame(self, frame_number: int) -> Optional[np.ndarray]:
        """获取指定帧"""
        try:
            if self.current_video is None or not self.video_info or not self.video_info.is_valid:
                return None
            
            # 检查帧号有效性
            if frame_number < 0 or frame_number >= self.video_info.frame_count:
                self.logger.warning(f"帧号超出范围: {frame_number}")
                return None
            
            # 设置帧位置
            self.current_video.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            # 读取帧
            ret, frame = self.current_video.read()
            
            if ret:
                return frame
            else:
                self.logger.warning(f"无法读取帧: {frame_number}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取帧时发生错误: {e}")
            return None
    
    def get_frame_at_time(self, time_seconds: float) -> Optional[np.ndarray]:
        """获取指定时间的帧"""
        if not self.video_info or not self.video_info.is_valid:
            return None
        
        frame_number = int(time_seconds * self.video_info.fps)
        return self.get_frame(frame_number)
    
    def get_thumbnail(self, max_size: Tuple[int, int] = (200, 150)) -> Optional[np.ndarray]:
        """获取缩略图（第一帧）"""
        try:
            frame = self.get_frame(0)
            if frame is None:
                return None
            
            # 计算缩放比例
            h, w = frame.shape[:2]
            max_w, max_h = max_size
            
            scale = min(max_w / w, max_h / h)
            
            if scale < 1.0:
                new_w = int(w * scale)
                new_h = int(h * scale)
                thumbnail = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_AREA)
            else:
                thumbnail = frame.copy()
            
            return thumbnail
            
        except Exception as e:
            self.logger.error(f"生成缩略图时发生错误: {e}")
            return None
    
    def get_frame_iterator(self, start_frame: int = 0, end_frame: Optional[int] = None):
        """获取帧迭代器"""
        if self.current_video is None or not self.video_info or not self.video_info.is_valid:
            return
        
        if end_frame is None:
            end_frame = self.video_info.frame_count
        
        # 设置起始位置
        self.current_video.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
        
        current_frame = start_frame
        while current_frame < end_frame:
            ret, frame = self.current_video.read()
            if not ret:
                break
            
            yield current_frame, frame
            current_frame += 1
    
    def seek_to_frame(self, frame_number: int) -> bool:
        """跳转到指定帧"""
        try:
            if self.current_video is None or not self.video_info or not self.video_info.is_valid:
                return False
            
            if frame_number < 0 or frame_number >= self.video_info.frame_count:
                return False
            
            self.current_video.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            return True
            
        except Exception as e:
            self.logger.error(f"跳转帧时发生错误: {e}")
            return False
    
    def seek_to_time(self, time_seconds: float) -> bool:
        """跳转到指定时间"""
        if not self.video_info or not self.video_info.is_valid:
            return False
        
        frame_number = int(time_seconds * self.video_info.fps)
        return self.seek_to_frame(frame_number)
    
    def is_loaded(self) -> bool:
        """检查是否已加载视频"""
        return (self.current_video is not None and 
                self.video_info is not None and 
                self.video_info.is_valid)
    
    def get_current_info(self) -> Optional[VideoInfo]:
        """获取当前视频信息"""
        return self.video_info
    
    def release(self):
        """释放视频资源"""
        if self.current_video is not None:
            self.current_video.release()
            self.current_video = None
        
        self.video_info = None
        self.logger.debug("视频资源已释放")
    
    def __del__(self):
        """析构函数"""
        self.release()
