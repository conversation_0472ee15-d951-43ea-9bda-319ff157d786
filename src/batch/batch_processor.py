"""
批量处理器
Batch Processor
"""

import os
import time
import json
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import threading
from queue import Queue, Empty

from ..utils.logger import Logger
from ..fusion.fusion_engine import FusionEngine, FusionParams, FusionType
from ..utils.performance_monitor import PerformanceMonitor
from ..utils.memory_manager import MemoryManager


class BatchTaskStatus(Enum):
    """批量任务状态"""
    PENDING = "pending"      # 等待中
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"   # 已取消


@dataclass
class BatchTask:
    """批量任务数据类"""
    task_id: str
    video_a_path: str
    video_b_path: str
    output_path: str
    fusion_params: Dict[str, Any]
    status: BatchTaskStatus = BatchTaskStatus.PENDING
    progress: float = 0.0
    error_message: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BatchTask':
        """从字典创建"""
        data['status'] = BatchTaskStatus(data['status'])
        return cls(**data)


class BatchProcessor:
    """批量处理器"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        
        # 任务队列
        self.task_queue: Queue = Queue()
        self.tasks: Dict[str, BatchTask] = {}
        
        # 处理状态
        self.is_processing = False
        self.current_task: Optional[BatchTask] = None
        self.worker_thread: Optional[threading.Thread] = None
        
        # 性能组件
        self.performance_monitor = PerformanceMonitor()
        self.memory_manager = MemoryManager()
        
        # 回调函数
        self.progress_callback: Optional[Callable[[str, float, str], None]] = None
        self.task_completed_callback: Optional[Callable[[BatchTask], None]] = None
        self.batch_completed_callback: Optional[Callable[[Dict[str, Any]], None]] = None
        
        # 统计信息
        self.total_tasks = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.start_time: Optional[float] = None
        
        self.logger.info("批量处理器初始化完成")
    
    def add_task(self, task: BatchTask) -> bool:
        """添加批量任务"""
        try:
            # 验证任务
            if not self._validate_task(task):
                return False
            
            # 添加到队列和字典
            self.tasks[task.task_id] = task
            self.task_queue.put(task.task_id)
            self.total_tasks += 1
            
            self.logger.info(f"批量任务已添加: {task.task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加批量任务失败: {e}")
            return False
    
    def add_tasks_from_config(self, config_path: str) -> int:
        """从配置文件添加批量任务"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            added_count = 0
            for task_data in config.get('tasks', []):
                task = BatchTask.from_dict(task_data)
                if self.add_task(task):
                    added_count += 1
            
            self.logger.info(f"从配置文件添加了 {added_count} 个批量任务")
            return added_count
            
        except Exception as e:
            self.logger.error(f"从配置文件添加任务失败: {e}")
            return 0
    
    def create_batch_from_folders(self, video_a_folder: str, video_b_folder: str, 
                                 output_folder: str, fusion_params: Dict[str, Any]) -> int:
        """从文件夹创建批量任务"""
        try:
            video_a_folder = Path(video_a_folder)
            video_b_folder = Path(video_b_folder)
            output_folder = Path(output_folder)
            
            # 确保输出文件夹存在
            output_folder.mkdir(parents=True, exist_ok=True)
            
            # 获取视频文件列表
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
            
            a_files = []
            for ext in video_extensions:
                a_files.extend(video_a_folder.glob(f'*{ext}'))
                a_files.extend(video_a_folder.glob(f'*{ext.upper()}'))
            
            b_files = []
            for ext in video_extensions:
                b_files.extend(video_b_folder.glob(f'*{ext}'))
                b_files.extend(video_b_folder.glob(f'*{ext.upper()}'))
            
            # 创建任务
            added_count = 0
            min_count = min(len(a_files), len(b_files))
            
            for i in range(min_count):
                a_file = a_files[i]
                b_file = b_files[i]
                
                # 生成输出文件名
                output_name = f"batch_fusion_{i+1}_{a_file.stem}_{b_file.stem}.mp4"
                output_path = output_folder / output_name
                
                # 创建任务
                task = BatchTask(
                    task_id=f"batch_task_{i+1}",
                    video_a_path=str(a_file),
                    video_b_path=str(b_file),
                    output_path=str(output_path),
                    fusion_params=fusion_params.copy()
                )
                
                if self.add_task(task):
                    added_count += 1
            
            self.logger.info(f"从文件夹创建了 {added_count} 个批量任务")
            return added_count
            
        except Exception as e:
            self.logger.error(f"从文件夹创建批量任务失败: {e}")
            return 0
    
    def start_batch_processing(self) -> bool:
        """开始批量处理"""
        try:
            if self.is_processing:
                self.logger.warning("批量处理已在进行中")
                return False
            
            if self.task_queue.empty():
                self.logger.warning("没有待处理的任务")
                return False
            
            self.is_processing = True
            self.start_time = time.time()
            
            # 启动性能监控
            self.performance_monitor.start_monitoring()
            
            # 启动工作线程
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
            
            self.logger.info("批量处理已开始")
            return True
            
        except Exception as e:
            self.logger.error(f"启动批量处理失败: {e}")
            self.is_processing = False
            return False
    
    def stop_batch_processing(self) -> bool:
        """停止批量处理"""
        try:
            if not self.is_processing:
                return True
            
            self.is_processing = False
            
            # 等待工作线程结束
            if self.worker_thread and self.worker_thread.is_alive():
                self.worker_thread.join(timeout=5.0)
            
            # 停止性能监控
            self.performance_monitor.stop_monitoring()
            
            # 标记当前任务为取消
            if self.current_task:
                self.current_task.status = BatchTaskStatus.CANCELLED
                self.current_task.end_time = time.time()
            
            self.logger.info("批量处理已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止批量处理失败: {e}")
            return False
    
    def _worker_loop(self):
        """工作线程循环"""
        while self.is_processing:
            try:
                # 获取下一个任务
                try:
                    task_id = self.task_queue.get(timeout=1.0)
                except Empty:
                    continue
                
                task = self.tasks.get(task_id)
                if not task:
                    continue
                
                # 处理任务
                self.current_task = task
                self._process_single_task(task)
                
                # 任务完成回调
                if self.task_completed_callback:
                    self.task_completed_callback(task)
                
                # 检查是否所有任务完成
                if self.task_queue.empty():
                    self._on_batch_completed()
                    break
                    
            except Exception as e:
                self.logger.error(f"工作线程错误: {e}")
                if self.current_task:
                    self.current_task.status = BatchTaskStatus.FAILED
                    self.current_task.error_message = str(e)
                    self.current_task.end_time = time.time()
                    self.failed_tasks += 1
        
        self.is_processing = False
        self.current_task = None
    
    def _process_single_task(self, task: BatchTask):
        """处理单个任务"""
        try:
            task.status = BatchTaskStatus.PROCESSING
            task.start_time = time.time()
            
            self.logger.info(f"开始处理任务: {task.task_id}")
            
            # 更新进度回调
            if self.progress_callback:
                self.progress_callback(task.task_id, 0.0, "开始处理")
            
            # 创建融合引擎
            engine = FusionEngine()
            
            # 设置性能优化
            engine.set_performance_settings(
                enable_monitoring=True,
                enable_multithreading=True,
                enable_memory_optimization=True
            )
            
            # 加载视频
            if self.progress_callback:
                self.progress_callback(task.task_id, 10.0, "加载视频A")
            
            if not engine.load_video_a(task.video_a_path):
                raise Exception(f"无法加载视频A: {task.video_a_path}")
            
            if self.progress_callback:
                self.progress_callback(task.task_id, 20.0, "加载视频B")
            
            if not engine.load_video_b(task.video_b_path):
                raise Exception(f"无法加载视频B: {task.video_b_path}")
            
            # 设置融合参数
            if self.progress_callback:
                self.progress_callback(task.task_id, 30.0, "设置融合参数")
            
            fusion_params = FusionParams()
            
            # 从字典设置参数
            params = task.fusion_params
            if params.get('fusion_type') == 'insertion':
                fusion_params.fusion_type = FusionType.INSERTION
            elif params.get('fusion_type') == 'overlay':
                fusion_params.fusion_type = FusionType.OVERLAY
            elif params.get('fusion_type') == 'blend':
                fusion_params.fusion_type = FusionType.BLEND
            
            fusion_params.alpha = params.get('alpha', 0.5)
            fusion_params.resize_mode = params.get('resize_mode', 'fit')
            fusion_params.output_fps = params.get('output_fps', 30.0)
            
            engine.set_fusion_params(fusion_params)
            
            # 执行融合
            if self.progress_callback:
                self.progress_callback(task.task_id, 40.0, "执行融合")
            
            success = engine.execute_fusion()
            
            if not success:
                raise Exception("融合处理失败")
            
            # 保存结果
            if self.progress_callback:
                self.progress_callback(task.task_id, 80.0, "保存结果")
            
            # 确保输出目录存在
            output_dir = Path(task.output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            if not engine.save_result(task.output_path):
                raise Exception(f"无法保存结果到: {task.output_path}")
            
            # 任务完成
            task.status = BatchTaskStatus.COMPLETED
            task.end_time = time.time()
            task.progress = 100.0
            self.completed_tasks += 1
            
            if self.progress_callback:
                self.progress_callback(task.task_id, 100.0, "任务完成")
            
            self.logger.info(f"任务完成: {task.task_id}")
            
        except Exception as e:
            task.status = BatchTaskStatus.FAILED
            task.error_message = str(e)
            task.end_time = time.time()
            self.failed_tasks += 1
            
            if self.progress_callback:
                self.progress_callback(task.task_id, 0.0, f"任务失败: {str(e)}")
            
            self.logger.error(f"任务失败 {task.task_id}: {e}")
    
    def _validate_task(self, task: BatchTask) -> bool:
        """验证任务"""
        try:
            # 检查视频文件是否存在
            if not Path(task.video_a_path).exists():
                self.logger.error(f"视频A文件不存在: {task.video_a_path}")
                return False
            
            if not Path(task.video_b_path).exists():
                self.logger.error(f"视频B文件不存在: {task.video_b_path}")
                return False
            
            # 检查任务ID是否重复
            if task.task_id in self.tasks:
                self.logger.error(f"任务ID重复: {task.task_id}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"任务验证失败: {e}")
            return False
    
    def _on_batch_completed(self):
        """批量处理完成"""
        try:
            end_time = time.time()
            total_time = end_time - self.start_time if self.start_time else 0
            
            # 生成统计报告
            stats = {
                "total_tasks": self.total_tasks,
                "completed_tasks": self.completed_tasks,
                "failed_tasks": self.failed_tasks,
                "success_rate": (self.completed_tasks / self.total_tasks * 100) if self.total_tasks > 0 else 0,
                "total_time": total_time,
                "average_time_per_task": total_time / self.total_tasks if self.total_tasks > 0 else 0
            }
            
            # 批量完成回调
            if self.batch_completed_callback:
                self.batch_completed_callback(stats)
            
            self.logger.info(f"批量处理完成 - 总任务: {self.total_tasks}, "
                           f"成功: {self.completed_tasks}, 失败: {self.failed_tasks}")
            
        except Exception as e:
            self.logger.error(f"批量完成处理错误: {e}")
    
    def get_task_status(self, task_id: str) -> Optional[BatchTask]:
        """获取任务状态"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[BatchTask]:
        """获取所有任务"""
        return list(self.tasks.values())
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "pending_tasks": self.task_queue.qsize(),
            "is_processing": self.is_processing,
            "current_task": self.current_task.task_id if self.current_task else None
        }
    
    def export_batch_config(self, config_path: str) -> bool:
        """导出批量配置"""
        try:
            config = {
                "tasks": [task.to_dict() for task in self.tasks.values()]
            }
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"批量配置已导出: {config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出批量配置失败: {e}")
            return False
    
    def clear_tasks(self):
        """清空所有任务"""
        if self.is_processing:
            self.logger.warning("批量处理进行中，无法清空任务")
            return
        
        # 清空队列
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
            except Empty:
                break
        
        # 清空任务字典
        self.tasks.clear()
        
        # 重置统计
        self.total_tasks = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        
        self.logger.info("所有任务已清空")
    
    def set_progress_callback(self, callback: Callable[[str, float, str], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_task_completed_callback(self, callback: Callable[[BatchTask], None]):
        """设置任务完成回调函数"""
        self.task_completed_callback = callback
    
    def set_batch_completed_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """设置批量完成回调函数"""
        self.batch_completed_callback = callback
