"""
性能监控器
Performance Monitor
"""

import time
import psutil
import threading
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from collections import deque
import numpy as np

from .logger import Logger


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    gpu_memory_mb: Optional[float] = None
    processing_fps: Optional[float] = None
    frame_count: Optional[int] = None


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 1000):
        self.logger = Logger.get_logger(__name__)
        self.max_history = max_history
        self.metrics_history: deque = deque(maxlen=max_history)
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.monitor_interval = 1.0  # 监控间隔（秒）
        
        # 性能统计
        self.start_time: Optional[float] = None
        self.frame_count = 0
        self.processing_start_time: Optional[float] = None
        
        # 回调函数
        self.metrics_callback: Optional[Callable[[PerformanceMetrics], None]] = None
        
        self.logger.info("性能监控器初始化完成")
    
    def start_monitoring(self, interval: float = 1.0):
        """开始性能监控"""
        if self.is_monitoring:
            self.logger.warning("性能监控已在运行")
            return
        
        self.monitor_interval = interval
        self.is_monitoring = True
        self.start_time = time.time()
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info(f"开始性能监控，间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止性能监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        
        self.logger.info("性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # 调用回调函数
                if self.metrics_callback:
                    self.metrics_callback(metrics)
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                self.logger.error(f"性能监控错误: {e}")
                time.sleep(self.monitor_interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        current_time = time.time()
        
        # CPU和内存使用率
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / (1024 * 1024)
        
        # GPU内存（如果可用）
        gpu_memory_mb = self._get_gpu_memory()
        
        # 处理帧率
        processing_fps = self._calculate_fps()
        
        return PerformanceMetrics(
            timestamp=current_time,
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            gpu_memory_mb=gpu_memory_mb,
            processing_fps=processing_fps,
            frame_count=self.frame_count
        )
    
    def _get_gpu_memory(self) -> Optional[float]:
        """获取GPU内存使用情况"""
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            if gpus:
                return gpus[0].memoryUsed
        except ImportError:
            pass
        except Exception as e:
            self.logger.debug(f"获取GPU内存失败: {e}")
        
        return None
    
    def _calculate_fps(self) -> Optional[float]:
        """计算处理帧率"""
        if not self.processing_start_time or self.frame_count == 0:
            return None
        
        elapsed_time = time.time() - self.processing_start_time
        if elapsed_time > 0:
            return self.frame_count / elapsed_time
        
        return None
    
    def start_processing(self):
        """开始处理计时"""
        self.processing_start_time = time.time()
        self.frame_count = 0
        self.logger.debug("开始处理计时")
    
    def update_frame_count(self, count: int = 1):
        """更新处理帧数"""
        self.frame_count += count
    
    def stop_processing(self) -> Dict[str, float]:
        """停止处理计时并返回统计信息"""
        if not self.processing_start_time:
            return {}
        
        elapsed_time = time.time() - self.processing_start_time
        fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
        
        stats = {
            "elapsed_time": elapsed_time,
            "frame_count": self.frame_count,
            "average_fps": fps
        }
        
        self.logger.info(f"处理完成 - 耗时: {elapsed_time:.2f}秒, 帧数: {self.frame_count}, 平均FPS: {fps:.2f}")
        
        self.processing_start_time = None
        self.frame_count = 0
        
        return stats
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前性能指标"""
        if not self.metrics_history:
            return None
        return self.metrics_history[-1]
    
    def get_average_metrics(self, last_n: int = 10) -> Dict[str, float]:
        """获取平均性能指标"""
        if not self.metrics_history:
            return {}
        
        recent_metrics = list(self.metrics_history)[-last_n:]
        
        if not recent_metrics:
            return {}
        
        avg_cpu = np.mean([m.cpu_percent for m in recent_metrics])
        avg_memory = np.mean([m.memory_percent for m in recent_metrics])
        avg_memory_mb = np.mean([m.memory_used_mb for m in recent_metrics])
        
        result = {
            "avg_cpu_percent": avg_cpu,
            "avg_memory_percent": avg_memory,
            "avg_memory_used_mb": avg_memory_mb
        }
        
        # GPU内存平均值（如果有数据）
        gpu_values = [m.gpu_memory_mb for m in recent_metrics if m.gpu_memory_mb is not None]
        if gpu_values:
            result["avg_gpu_memory_mb"] = np.mean(gpu_values)
        
        # 处理FPS平均值（如果有数据）
        fps_values = [m.processing_fps for m in recent_metrics if m.processing_fps is not None]
        if fps_values:
            result["avg_processing_fps"] = np.mean(fps_values)
        
        return result
    
    def get_peak_metrics(self) -> Dict[str, float]:
        """获取峰值性能指标"""
        if not self.metrics_history:
            return {}
        
        metrics_list = list(self.metrics_history)
        
        result = {
            "peak_cpu_percent": max(m.cpu_percent for m in metrics_list),
            "peak_memory_percent": max(m.memory_percent for m in metrics_list),
            "peak_memory_used_mb": max(m.memory_used_mb for m in metrics_list)
        }
        
        # GPU内存峰值（如果有数据）
        gpu_values = [m.gpu_memory_mb for m in metrics_list if m.gpu_memory_mb is not None]
        if gpu_values:
            result["peak_gpu_memory_mb"] = max(gpu_values)
        
        # 处理FPS峰值（如果有数据）
        fps_values = [m.processing_fps for m in metrics_list if m.processing_fps is not None]
        if fps_values:
            result["peak_processing_fps"] = max(fps_values)
        
        return result
    
    def set_metrics_callback(self, callback: Callable[[PerformanceMetrics], None]):
        """设置性能指标回调函数"""
        self.metrics_callback = callback
    
    def clear_history(self):
        """清空历史记录"""
        self.metrics_history.clear()
        self.logger.info("性能监控历史记录已清空")
    
    def export_metrics(self, filename: str):
        """导出性能指标到文件"""
        try:
            import csv
            
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'timestamp', 'cpu_percent', 'memory_percent', 'memory_used_mb',
                    'gpu_memory_mb', 'processing_fps', 'frame_count'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for metrics in self.metrics_history:
                    writer.writerow({
                        'timestamp': metrics.timestamp,
                        'cpu_percent': metrics.cpu_percent,
                        'memory_percent': metrics.memory_percent,
                        'memory_used_mb': metrics.memory_used_mb,
                        'gpu_memory_mb': metrics.gpu_memory_mb,
                        'processing_fps': metrics.processing_fps,
                        'frame_count': metrics.frame_count
                    })
            
            self.logger.info(f"性能指标已导出到: {filename}")
            
        except Exception as e:
            self.logger.error(f"导出性能指标失败: {e}")
    
    def get_memory_usage_mb(self) -> float:
        """获取当前内存使用量（MB）"""
        return psutil.virtual_memory().used / (1024 * 1024)
    
    def get_available_memory_mb(self) -> float:
        """获取可用内存量（MB）"""
        return psutil.virtual_memory().available / (1024 * 1024)
    
    def is_memory_critical(self, threshold_percent: float = 90.0) -> bool:
        """检查内存使用是否达到临界值"""
        memory_percent = psutil.virtual_memory().percent
        return memory_percent >= threshold_percent
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            return {
                "cpu_count": psutil.cpu_count(),
                "cpu_count_logical": psutil.cpu_count(logical=True),
                "total_memory_gb": psutil.virtual_memory().total / (1024**3),
                "platform": psutil.WINDOWS if hasattr(psutil, 'WINDOWS') else "Unknown"
            }
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {}


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self, monitor: PerformanceMonitor):
        self.monitor = monitor
        self.logger = Logger.get_logger(__name__)
        
        # 优化建议
        self.optimization_suggestions: List[str] = []
        
        self.logger.info("性能优化器初始化完成")
    
    def analyze_performance(self) -> Dict[str, Any]:
        """分析性能并提供优化建议"""
        try:
            current_metrics = self.monitor.get_current_metrics()
            average_metrics = self.monitor.get_average_metrics()
            peak_metrics = self.monitor.get_peak_metrics()
            
            if not current_metrics or not average_metrics:
                return {"status": "insufficient_data"}
            
            analysis = {
                "current": current_metrics,
                "average": average_metrics,
                "peak": peak_metrics,
                "suggestions": []
            }
            
            # 分析CPU使用率
            if average_metrics.get("avg_cpu_percent", 0) > 80:
                analysis["suggestions"].append("CPU使用率过高，建议减少并发处理或优化算法")
            
            # 分析内存使用率
            if average_metrics.get("avg_memory_percent", 0) > 85:
                analysis["suggestions"].append("内存使用率过高，建议增加内存或优化内存使用")
            
            # 分析处理速度
            if average_metrics.get("avg_processing_fps", 0) < 10:
                analysis["suggestions"].append("处理速度较慢，建议优化算法或使用GPU加速")
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"性能分析失败: {e}")
            return {"status": "error", "message": str(e)}
    
    def suggest_optimizations(self, video_info: Dict[str, Any]) -> List[str]:
        """根据视频信息提供优化建议"""
        suggestions = []
        
        try:
            # 根据视频尺寸提供建议
            if "width" in video_info and "height" in video_info:
                resolution = video_info["width"] * video_info["height"]
                
                if resolution > 1920 * 1080:  # 大于1080p
                    suggestions.append("视频分辨率较高，建议先降低分辨率进行预览")
                
                if resolution > 3840 * 2160:  # 大于4K
                    suggestions.append("4K视频处理需要大量内存，建议分段处理")
            
            # 根据帧数提供建议
            if "frame_count" in video_info:
                frame_count = video_info["frame_count"]
                
                if frame_count > 10000:  # 超过10000帧
                    suggestions.append("视频帧数较多，建议使用批量处理模式")
            
            # 根据文件大小提供建议
            if "file_size_mb" in video_info:
                file_size = video_info["file_size_mb"]
                
                if file_size > 1000:  # 大于1GB
                    suggestions.append("视频文件较大，建议确保有足够的可用内存")
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"生成优化建议失败: {e}")
            return ["无法生成优化建议"]
