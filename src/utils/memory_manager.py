"""
内存管理器
Memory Manager
"""

import gc
import sys
import psutil
import threading
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from collections import defaultdict
import numpy as np

from .logger import Logger


@dataclass
class MemoryInfo:
    """内存信息数据类"""
    total_mb: float
    available_mb: float
    used_mb: float
    percent: float
    process_mb: float


class MemoryCache:
    """内存缓存管理"""
    
    def __init__(self, max_size_mb: float = 500):
        self.max_size_mb = max_size_mb
        self.cache: Dict[str, Any] = {}
        self.cache_sizes: Dict[str, float] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.Lock()
        self.logger = Logger.get_logger(__name__)
        
        self.logger.info(f"内存缓存初始化，最大大小: {max_size_mb}MB")
    
    def put(self, key: str, value: Any, size_mb: Optional[float] = None) -> bool:
        """添加到缓存"""
        with self.lock:
            # 估算大小
            if size_mb is None:
                size_mb = self._estimate_size(value)
            
            # 检查是否超过最大大小
            if size_mb > self.max_size_mb:
                self.logger.warning(f"对象太大无法缓存: {size_mb}MB > {self.max_size_mb}MB")
                return False
            
            # 清理空间
            self._make_space(size_mb)
            
            # 添加到缓存
            self.cache[key] = value
            self.cache_sizes[key] = size_mb
            self.access_times[key] = time.time()
            
            self.logger.debug(f"缓存添加: {key}, 大小: {size_mb:.2f}MB")
            return True
    
    def get(self, key: str) -> Optional[Any]:
        """从缓存获取"""
        with self.lock:
            if key in self.cache:
                self.access_times[key] = time.time()
                self.logger.debug(f"缓存命中: {key}")
                return self.cache[key]
            
            self.logger.debug(f"缓存未命中: {key}")
            return None
    
    def remove(self, key: str) -> bool:
        """从缓存移除"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                del self.cache_sizes[key]
                del self.access_times[key]
                self.logger.debug(f"缓存移除: {key}")
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.cache_sizes.clear()
            self.access_times.clear()
            self.logger.info("缓存已清空")
    
    def _estimate_size(self, obj: Any) -> float:
        """估算对象大小（MB）"""
        try:
            if isinstance(obj, np.ndarray):
                return obj.nbytes / (1024 * 1024)
            else:
                return sys.getsizeof(obj) / (1024 * 1024)
        except:
            return 1.0  # 默认1MB
    
    def _make_space(self, required_mb: float):
        """为新对象腾出空间"""
        current_size = sum(self.cache_sizes.values())
        
        while current_size + required_mb > self.max_size_mb and self.cache:
            # 找到最久未访问的项目
            oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            
            # 移除最久未访问的项目
            removed_size = self.cache_sizes[oldest_key]
            del self.cache[oldest_key]
            del self.cache_sizes[oldest_key]
            del self.access_times[oldest_key]
            
            current_size -= removed_size
            self.logger.debug(f"缓存清理: {oldest_key}, 释放: {removed_size:.2f}MB")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_size = sum(self.cache_sizes.values())
            return {
                "items": len(self.cache),
                "total_size_mb": total_size,
                "max_size_mb": self.max_size_mb,
                "usage_percent": (total_size / self.max_size_mb) * 100 if self.max_size_mb > 0 else 0
            }


class MemoryManager:
    """内存管理器"""
    
    def __init__(self, cache_size_mb: float = 500):
        self.logger = Logger.get_logger(__name__)
        self.cache = MemoryCache(cache_size_mb)
        
        # 内存监控
        self.memory_threshold_percent = 85.0
        self.cleanup_callbacks: List[Callable[[], None]] = []
        
        # 统计信息
        self.gc_count = 0
        self.cleanup_count = 0
        
        self.logger.info("内存管理器初始化完成")
    
    def get_memory_info(self) -> MemoryInfo:
        """获取内存信息"""
        try:
            memory = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return MemoryInfo(
                total_mb=memory.total / (1024 * 1024),
                available_mb=memory.available / (1024 * 1024),
                used_mb=memory.used / (1024 * 1024),
                percent=memory.percent,
                process_mb=process_memory.rss / (1024 * 1024)
            )
        except Exception as e:
            self.logger.error(f"获取内存信息失败: {e}")
            return MemoryInfo(0, 0, 0, 0, 0)
    
    def is_memory_critical(self, threshold: Optional[float] = None) -> bool:
        """检查内存是否达到临界值"""
        threshold = threshold or self.memory_threshold_percent
        memory_info = self.get_memory_info()
        return memory_info.percent >= threshold
    
    def force_garbage_collection(self) -> Dict[str, int]:
        """强制垃圾回收"""
        try:
            # 执行垃圾回收
            collected = gc.collect()
            self.gc_count += 1
            
            # 获取垃圾回收统计
            stats = {
                "collected": collected,
                "generation_0": gc.get_count()[0],
                "generation_1": gc.get_count()[1],
                "generation_2": gc.get_count()[2]
            }
            
            self.logger.info(f"强制垃圾回收完成，回收对象: {collected}")
            return stats
            
        except Exception as e:
            self.logger.error(f"垃圾回收失败: {e}")
            return {"collected": 0}
    
    def cleanup_memory(self) -> bool:
        """清理内存"""
        try:
            initial_memory = self.get_memory_info()
            
            # 清空缓存
            self.cache.clear()
            
            # 强制垃圾回收
            self.force_garbage_collection()
            
            # 调用清理回调函数
            for callback in self.cleanup_callbacks:
                try:
                    callback()
                except Exception as e:
                    self.logger.error(f"清理回调函数错误: {e}")
            
            self.cleanup_count += 1
            
            final_memory = self.get_memory_info()
            freed_mb = initial_memory.process_mb - final_memory.process_mb
            
            self.logger.info(f"内存清理完成，释放: {freed_mb:.2f}MB")
            return True
            
        except Exception as e:
            self.logger.error(f"内存清理失败: {e}")
            return False
    
    def add_cleanup_callback(self, callback: Callable[[], None]):
        """添加清理回调函数"""
        self.cleanup_callbacks.append(callback)
    
    def remove_cleanup_callback(self, callback: Callable[[], None]):
        """移除清理回调函数"""
        if callback in self.cleanup_callbacks:
            self.cleanup_callbacks.remove(callback)
    
    def monitor_memory(self, interval: float = 5.0) -> threading.Thread:
        """启动内存监控线程"""
        def monitor_loop():
            while True:
                try:
                    if self.is_memory_critical():
                        self.logger.warning("内存使用率过高，执行自动清理")
                        self.cleanup_memory()
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    self.logger.error(f"内存监控错误: {e}")
                    time.sleep(interval)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        
        self.logger.info(f"内存监控已启动，间隔: {interval}秒")
        return monitor_thread
    
    def optimize_for_large_video(self, video_size_mb: float) -> Dict[str, Any]:
        """为大视频文件优化内存设置"""
        memory_info = self.get_memory_info()
        
        # 计算推荐设置
        available_memory = memory_info.available_mb
        
        # 为视频处理预留内存（至少是视频大小的2倍）
        required_memory = video_size_mb * 2
        
        recommendations = {
            "cache_size_mb": min(available_memory * 0.3, 1000),
            "chunk_size": 5 if video_size_mb > 1000 else 10,
            "enable_streaming": video_size_mb > available_memory * 0.5,
            "reduce_quality": video_size_mb > available_memory * 0.8
        }
        
        # 应用推荐设置
        if recommendations["cache_size_mb"] != self.cache.max_size_mb:
            self.cache.max_size_mb = recommendations["cache_size_mb"]
            self.cache.clear()  # 清空现有缓存
        
        self.logger.info(f"大视频优化设置: {recommendations}")
        return recommendations
    
    def get_memory_usage_by_type(self) -> Dict[str, float]:
        """获取按类型分类的内存使用情况"""
        try:
            import tracemalloc
            
            if not tracemalloc.is_tracing():
                tracemalloc.start()
                time.sleep(0.1)  # 等待一小段时间收集数据
            
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics('lineno')
            
            usage_by_file = defaultdict(float)
            
            for stat in top_stats[:20]:  # 只看前20个
                filename = stat.traceback.format()[-1].split('/')[-1]
                usage_by_file[filename] += stat.size / (1024 * 1024)  # 转换为MB
            
            return dict(usage_by_file)
            
        except Exception as e:
            self.logger.error(f"获取内存使用详情失败: {e}")
            return {}
    
    def estimate_video_memory_usage(self, width: int, height: int, 
                                   frame_count: int, channels: int = 3) -> float:
        """估算视频内存使用量（MB）"""
        # 每帧大小（字节）
        frame_size_bytes = width * height * channels
        
        # 总内存使用量（考虑处理过程中的临时数据）
        total_bytes = frame_size_bytes * frame_count * 2  # 乘以2考虑处理开销
        
        return total_bytes / (1024 * 1024)
    
    def get_optimal_processing_params(self, video_info: Dict[str, Any]) -> Dict[str, Any]:
        """获取最优处理参数"""
        memory_info = self.get_memory_info()
        
        # 估算视频内存使用量
        estimated_usage = self.estimate_video_memory_usage(
            video_info.get("width", 1920),
            video_info.get("height", 1080),
            video_info.get("frame_count", 1000)
        )
        
        # 计算最优参数
        available_memory = memory_info.available_mb
        
        params = {
            "use_threading": available_memory > 1000,
            "max_threads": min(4, max(1, int(available_memory / 500))),
            "chunk_size": max(1, int(available_memory / estimated_usage * 10)),
            "enable_caching": available_memory > estimated_usage * 1.5,
            "streaming_mode": estimated_usage > available_memory * 0.7
        }
        
        # 限制参数范围
        params["chunk_size"] = min(params["chunk_size"], 50)
        params["max_threads"] = min(params["max_threads"], 8)
        
        self.logger.info(f"最优处理参数: {params}")
        return params
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取内存管理统计信息"""
        memory_info = self.get_memory_info()
        cache_stats = self.cache.get_stats()
        
        return {
            "memory_info": {
                "total_mb": memory_info.total_mb,
                "available_mb": memory_info.available_mb,
                "used_percent": memory_info.percent,
                "process_mb": memory_info.process_mb
            },
            "cache_stats": cache_stats,
            "management_stats": {
                "gc_count": self.gc_count,
                "cleanup_count": self.cleanup_count,
                "threshold_percent": self.memory_threshold_percent
            }
        }
