"""
线程池管理器
Thread Pool Manager
"""

import threading
import queue
import time
from typing import Callable, Any, List, Optional, Dict
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from dataclasses import dataclass
import numpy as np

from .logger import Logger
from .performance_monitor import PerformanceMonitor


@dataclass
class TaskResult:
    """任务结果数据类"""
    task_id: str
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total_tasks: int):
        self.total_tasks = total_tasks
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.start_time = time.time()
        self.lock = threading.Lock()
        self.callbacks: List[Callable[[float, Dict[str, Any]], None]] = []
    
    def update(self, success: bool = True):
        """更新进度"""
        with self.lock:
            if success:
                self.completed_tasks += 1
            else:
                self.failed_tasks += 1
            
            # 计算进度信息
            total_processed = self.completed_tasks + self.failed_tasks
            progress = total_processed / self.total_tasks if self.total_tasks > 0 else 0
            
            elapsed_time = time.time() - self.start_time
            estimated_total_time = elapsed_time / progress if progress > 0 else 0
            remaining_time = estimated_total_time - elapsed_time
            
            progress_info = {
                "completed": self.completed_tasks,
                "failed": self.failed_tasks,
                "total": self.total_tasks,
                "elapsed_time": elapsed_time,
                "estimated_remaining_time": max(0, remaining_time),
                "tasks_per_second": total_processed / elapsed_time if elapsed_time > 0 else 0
            }
            
            # 调用回调函数
            for callback in self.callbacks:
                try:
                    callback(progress, progress_info)
                except Exception as e:
                    Logger.get_logger(__name__).error(f"进度回调函数错误: {e}")
    
    def add_callback(self, callback: Callable[[float, Dict[str, Any]], None]):
        """添加进度回调函数"""
        self.callbacks.append(callback)
    
    def is_complete(self) -> bool:
        """检查是否完成"""
        with self.lock:
            return (self.completed_tasks + self.failed_tasks) >= self.total_tasks


class ThreadPoolManager:
    """线程池管理器"""
    
    def __init__(self, max_workers: Optional[int] = None, monitor: Optional[PerformanceMonitor] = None):
        self.logger = Logger.get_logger(__name__)
        self.max_workers = max_workers or min(32, (threading.active_count() or 1) + 4)
        self.monitor = monitor
        
        self.executor: Optional[ThreadPoolExecutor] = None
        self.active_futures: Dict[str, Future] = {}
        self.results_queue = queue.Queue()
        
        # 统计信息
        self.total_tasks_submitted = 0
        self.total_tasks_completed = 0
        self.total_tasks_failed = 0
        
        self.logger.info(f"线程池管理器初始化完成，最大工作线程数: {self.max_workers}")
    
    def start(self):
        """启动线程池"""
        if self.executor is not None:
            self.logger.warning("线程池已经启动")
            return
        
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self.logger.info(f"线程池已启动，工作线程数: {self.max_workers}")
    
    def shutdown(self, wait: bool = True):
        """关闭线程池"""
        if self.executor is None:
            return
        
        # 取消所有未完成的任务
        for task_id, future in self.active_futures.items():
            if not future.done():
                future.cancel()
                self.logger.debug(f"取消任务: {task_id}")
        
        self.executor.shutdown(wait=wait)
        self.executor = None
        self.active_futures.clear()
        
        self.logger.info("线程池已关闭")
    
    def submit_task(self, task_id: str, func: Callable, *args, **kwargs) -> Future:
        """提交单个任务"""
        if self.executor is None:
            raise RuntimeError("线程池未启动")
        
        future = self.executor.submit(self._execute_task, task_id, func, *args, **kwargs)
        self.active_futures[task_id] = future
        self.total_tasks_submitted += 1
        
        self.logger.debug(f"提交任务: {task_id}")
        return future
    
    def submit_batch_tasks(self, tasks: List[Dict[str, Any]], 
                          progress_callback: Optional[Callable[[float, Dict[str, Any]], None]] = None) -> List[TaskResult]:
        """批量提交任务"""
        if self.executor is None:
            raise RuntimeError("线程池未启动")
        
        if not tasks:
            return []
        
        # 创建进度跟踪器
        tracker = ProgressTracker(len(tasks))
        if progress_callback:
            tracker.add_callback(progress_callback)
        
        # 提交所有任务
        futures = {}
        for task in tasks:
            task_id = task.get("task_id", f"task_{len(futures)}")
            func = task["func"]
            args = task.get("args", ())
            kwargs = task.get("kwargs", {})
            
            future = self.submit_task(task_id, func, *args, **kwargs)
            futures[future] = task_id
        
        # 等待所有任务完成
        results = []
        for future in as_completed(futures):
            task_id = futures[future]
            try:
                result = future.result()
                results.append(result)
                tracker.update(success=result.success)
                
            except Exception as e:
                error_result = TaskResult(
                    task_id=task_id,
                    success=False,
                    error=str(e)
                )
                results.append(error_result)
                tracker.update(success=False)
                self.logger.error(f"任务 {task_id} 执行失败: {e}")
            
            # 从活动任务中移除
            if task_id in self.active_futures:
                del self.active_futures[task_id]
        
        self.logger.info(f"批量任务完成，总数: {len(tasks)}, 成功: {tracker.completed_tasks}, 失败: {tracker.failed_tasks}")
        return results
    
    def _execute_task(self, task_id: str, func: Callable, *args, **kwargs) -> TaskResult:
        """执行单个任务"""
        start_time = time.time()
        
        try:
            # 性能监控
            if self.monitor:
                self.monitor.update_frame_count(1)
            
            # 执行任务
            result = func(*args, **kwargs)
            
            execution_time = time.time() - start_time
            
            task_result = TaskResult(
                task_id=task_id,
                success=True,
                result=result,
                execution_time=execution_time
            )
            
            self.total_tasks_completed += 1
            self.logger.debug(f"任务 {task_id} 执行成功，耗时: {execution_time:.3f}秒")
            
            return task_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            task_result = TaskResult(
                task_id=task_id,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
            
            self.total_tasks_failed += 1
            self.logger.error(f"任务 {task_id} 执行失败: {e}")
            
            return task_result
        
        finally:
            # 从活动任务中移除
            if task_id in self.active_futures:
                del self.active_futures[task_id]
    
    def cancel_task(self, task_id: str) -> bool:
        """取消指定任务"""
        if task_id in self.active_futures:
            future = self.active_futures[task_id]
            if future.cancel():
                del self.active_futures[task_id]
                self.logger.info(f"任务 {task_id} 已取消")
                return True
            else:
                self.logger.warning(f"任务 {task_id} 无法取消（可能已在执行中）")
                return False
        else:
            self.logger.warning(f"任务 {task_id} 不存在")
            return False
    
    def cancel_all_tasks(self) -> int:
        """取消所有任务"""
        cancelled_count = 0
        
        for task_id in list(self.active_futures.keys()):
            if self.cancel_task(task_id):
                cancelled_count += 1
        
        self.logger.info(f"已取消 {cancelled_count} 个任务")
        return cancelled_count
    
    def get_active_task_count(self) -> int:
        """获取活动任务数量"""
        return len(self.active_futures)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "max_workers": self.max_workers,
            "active_tasks": len(self.active_futures),
            "total_submitted": self.total_tasks_submitted,
            "total_completed": self.total_tasks_completed,
            "total_failed": self.total_tasks_failed,
            "success_rate": (self.total_tasks_completed / max(1, self.total_tasks_submitted)) * 100
        }
    
    def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """等待所有任务完成"""
        if not self.active_futures:
            return True
        
        try:
            # 等待所有活动任务完成
            futures = list(self.active_futures.values())
            for future in as_completed(futures, timeout=timeout):
                pass
            
            return True
            
        except TimeoutError:
            self.logger.warning(f"等待任务完成超时: {timeout}秒")
            return False
        except Exception as e:
            self.logger.error(f"等待任务完成时发生错误: {e}")
            return False


class VideoProcessingPool:
    """视频处理专用线程池"""
    
    def __init__(self, max_workers: Optional[int] = None, monitor: Optional[PerformanceMonitor] = None):
        self.thread_pool = ThreadPoolManager(max_workers, monitor)
        self.logger = Logger.get_logger(__name__)
        
        # 视频处理特定配置
        self.chunk_size = 10  # 每个任务处理的帧数
        self.memory_limit_mb = 1000  # 内存限制（MB）
        
        self.logger.info("视频处理线程池初始化完成")
    
    def start(self):
        """启动视频处理线程池"""
        self.thread_pool.start()
    
    def shutdown(self, wait: bool = True):
        """关闭视频处理线程池"""
        self.thread_pool.shutdown(wait)
    
    def process_frames_parallel(self, frames: List[np.ndarray], 
                               process_func: Callable[[np.ndarray], np.ndarray],
                               progress_callback: Optional[Callable[[float, Dict[str, Any]], None]] = None) -> List[np.ndarray]:
        """并行处理视频帧"""
        if not frames:
            return []
        
        # 检查内存使用情况
        if self.thread_pool.monitor and self.thread_pool.monitor.is_memory_critical():
            self.logger.warning("内存使用率过高，使用单线程处理")
            return [process_func(frame) for frame in frames]
        
        # 创建任务列表
        tasks = []
        for i, frame in enumerate(frames):
            tasks.append({
                "task_id": f"frame_{i}",
                "func": process_func,
                "args": (frame,)
            })
        
        # 批量处理
        results = self.thread_pool.submit_batch_tasks(tasks, progress_callback)
        
        # 按顺序提取结果
        processed_frames = []
        for i, result in enumerate(results):
            if result.success:
                processed_frames.append(result.result)
            else:
                self.logger.error(f"帧 {i} 处理失败: {result.error}")
                # 使用原始帧作为备用
                processed_frames.append(frames[i])
        
        return processed_frames
    
    def process_video_chunks(self, video_frames: List[np.ndarray],
                           process_func: Callable[[List[np.ndarray]], List[np.ndarray]],
                           progress_callback: Optional[Callable[[float, Dict[str, Any]], None]] = None) -> List[np.ndarray]:
        """分块并行处理视频"""
        if not video_frames:
            return []
        
        # 将视频分块
        chunks = []
        for i in range(0, len(video_frames), self.chunk_size):
            chunk = video_frames[i:i + self.chunk_size]
            chunks.append(chunk)
        
        # 创建任务列表
        tasks = []
        for i, chunk in enumerate(chunks):
            tasks.append({
                "task_id": f"chunk_{i}",
                "func": process_func,
                "args": (chunk,)
            })
        
        # 批量处理
        results = self.thread_pool.submit_batch_tasks(tasks, progress_callback)
        
        # 合并结果
        processed_frames = []
        for result in results:
            if result.success:
                processed_frames.extend(result.result)
            else:
                self.logger.error(f"块处理失败: {result.error}")
        
        return processed_frames
    
    def set_chunk_size(self, size: int):
        """设置块大小"""
        self.chunk_size = max(1, size)
        self.logger.info(f"块大小设置为: {self.chunk_size}")
    
    def set_memory_limit(self, limit_mb: int):
        """设置内存限制"""
        self.memory_limit_mb = max(100, limit_mb)
        self.logger.info(f"内存限制设置为: {self.memory_limit_mb}MB")
    
    def get_optimal_chunk_size(self, frame_count: int, available_memory_mb: float) -> int:
        """计算最优块大小"""
        # 估算每帧内存使用量（假设1080p RGB图像约6MB）
        estimated_frame_size_mb = 6
        
        # 计算可以同时处理的帧数
        max_frames_in_memory = int(available_memory_mb * 0.5 / estimated_frame_size_mb)
        
        # 考虑线程数量
        optimal_chunk_size = max(1, max_frames_in_memory // self.thread_pool.max_workers)
        
        # 限制在合理范围内
        optimal_chunk_size = min(optimal_chunk_size, 50)
        optimal_chunk_size = max(optimal_chunk_size, 1)
        
        self.logger.info(f"计算最优块大小: {optimal_chunk_size}")
        return optimal_chunk_size
