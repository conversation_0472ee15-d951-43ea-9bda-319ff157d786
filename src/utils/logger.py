"""
日志管理器
Logger Manager
"""

import logging
import os
from datetime import datetime


class Logger:
    """日志管理器类"""
    
    _loggers = {}
    
    @classmethod
    def get_logger(cls, name: str, level: str = "INFO") -> logging.Logger:
        """获取日志器实例"""
        if name not in cls._loggers:
            cls._loggers[name] = cls._create_logger(name, level)
        return cls._loggers[name]
    
    @classmethod
    def _create_logger(cls, name: str, level: str) -> logging.Logger:
        """创建日志器"""
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper()))
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 创建日志目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 创建文件处理器
        log_file = os.path.join(log_dir, f"video_fusion_editor_{datetime.now().strftime('%Y%m%d')}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
