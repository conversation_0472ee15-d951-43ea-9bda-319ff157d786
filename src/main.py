#!/usr/bin/env python3
"""
视频融合编辑器主程序入口
Video Fusion Editor Main Entry Point
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.gui.main_window import MainWindow
from src.utils.logger import Logger


def main():
    """主函数"""
    # 初始化日志
    logger = Logger.get_logger(__name__)
    logger.info("启动视频融合编辑器")
    
    # 创建QApplication实例
    app = QApplication(sys.argv)
    app.setApplicationName("视频融合编辑器")
    app.setApplicationVersion("1.0.0")
    
    # 设置高DPI支持
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    try:
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        logger.info("主窗口已显示")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
