#!/usr/bin/env python3
"""
测试预览和融合功能的脚本
验证修复后的预览生成和融合处理功能
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.video.video_loader import VideoLoader
from src.fusion.fusion_engine import FusionEngine, FusionParams, FusionType
from src.fusion.insertion_fusion import InsertionPosition

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_preview_generation():
    """测试预览生成功能"""
    logger.info("=== 测试预览生成功能 ===")
    
    # 创建融合引擎
    fusion_engine = FusionEngine()
    
    # 加载视频
    video_a_path = "videos/172681-849651720_tiny.mp4"
    video_b_path = "videos/283533_medium.mp4"
    
    if not fusion_engine.load_video_a(video_a_path):
        logger.error("A视频加载失败")
        return False
    
    if not fusion_engine.load_video_b(video_b_path):
        logger.error("B视频加载失败")
        return False
    
    # 设置融合参数
    fusion_params = FusionParams()
    fusion_params.fusion_type = FusionType.INSERTION
    fusion_params.alpha = 0.8
    fusion_params.resize_mode = "fit"
    
    # 设置插入位置
    positions = [
        InsertionPosition(frame_number=300, duration=1),
        InsertionPosition(frame_number=600, duration=1),
        InsertionPosition(frame_number=900, duration=1)
    ]
    fusion_params.positions = positions
    
    fusion_engine.set_fusion_params(fusion_params)
    logger.info(f"融合参数设置完成，插入位置数量: {len(positions)}")
    
    # 生成预览
    preview_frames = fusion_engine.get_fusion_preview(max_frames=3)
    
    if preview_frames:
        logger.info(f"✅ 预览生成成功，预览帧数: {len(preview_frames)}")
        
        # 保存预览图像
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        import cv2
        for i, (frame_index, frame) in enumerate(preview_frames):
            preview_path = output_dir / f"test_preview_{i}_frame_{frame_index}.jpg"
            cv2.imwrite(str(preview_path), frame)
            logger.info(f"预览图像已保存: {preview_path}")
        
        return True
    else:
        logger.error("❌ 预览生成失败")
        return False

def test_fusion_processing():
    """测试融合处理功能"""
    logger.info("=== 测试融合处理功能 ===")
    
    # 创建融合引擎
    fusion_engine = FusionEngine()
    
    # 加载视频
    video_a_path = "videos/172681-849651720_tiny.mp4"
    video_b_path = "videos/283533_medium.mp4"
    
    if not fusion_engine.load_video_a(video_a_path):
        logger.error("A视频加载失败")
        return False
    
    if not fusion_engine.load_video_b(video_b_path):
        logger.error("B视频加载失败")
        return False
    
    # 设置融合参数
    fusion_params = FusionParams()
    fusion_params.fusion_type = FusionType.INSERTION
    fusion_params.alpha = 0.8
    fusion_params.resize_mode = "fit"
    fusion_params.output_fps = 30.0
    fusion_params.output_codec = "mp4v"
    
    # 设置插入位置（只处理少量帧以加快测试）
    positions = [
        InsertionPosition(frame_number=300, duration=1),
        InsertionPosition(frame_number=600, duration=1)
    ]
    fusion_params.positions = positions
    
    fusion_engine.set_fusion_params(fusion_params)
    
    # 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 设置输出路径
    output_path = output_dir / "test_preview_fusion_result.mp4"
    
    # 执行融合处理
    logger.info("开始融合处理...")
    try:
        # 这里我们只处理前10秒来加快测试
        success = fusion_engine.process_fusion(
            str(output_path),
            max_duration=10.0  # 只处理前10秒
        )
        
        if success:
            logger.info(f"✅ 融合处理成功，输出文件: {output_path}")
            
            # 检查输出文件
            if output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"输出文件大小: {file_size / 1024:.2f} KB")
                return True
            else:
                logger.error("输出文件不存在")
                return False
        else:
            logger.error("❌ 融合处理失败")
            return False
            
    except Exception as e:
        logger.error(f"融合处理异常: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始预览和融合功能测试")
    
    # 检查当前工作目录
    current_dir = os.getcwd()
    logger.info(f"当前工作目录: {current_dir}")
    
    # 检查视频文件是否存在
    video_a_path = "videos/172681-849651720_tiny.mp4"
    video_b_path = "videos/283533_medium.mp4"
    
    if not os.path.exists(video_a_path):
        logger.error(f"A视频文件不存在: {video_a_path}")
        return False
    
    if not os.path.exists(video_b_path):
        logger.error(f"B视频文件不存在: {video_b_path}")
        return False
    
    # 执行测试
    tests = [
        ("预览生成功能", test_preview_generation),
        ("融合处理功能", test_fusion_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"执行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    # 输出测试结果
    logger.info(f"\n{'='*50}")
    logger.info(f"测试完成: {passed}/{total} 通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！预览和融合功能正常")
        return True
    else:
        logger.error(f"⚠️  有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
