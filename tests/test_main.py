#!/usr/bin/env python3
"""
主程序测试
"""

import sys
import os
import pytest
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 在测试前激活环境
activate_script = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'activate_env.sh')
if os.path.exists(activate_script):
    os.system(f'source {activate_script}')


class TestMain:
    """主程序测试类"""
    
    @pytest.fixture(scope="session")
    def qapp(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        app.quit()
    
    def test_import_main_module(self):
        """测试主模块导入"""
        try:
            from src import main
            assert main is not None
        except ImportError as e:
            pytest.fail(f"无法导入主模块: {e}")
    
    def test_application_creation(self, qapp):
        """测试应用程序创建"""
        assert qapp is not None
        assert qapp.applicationName() == "视频融合编辑器" or qapp.applicationName() == ""


if __name__ == "__main__":
    pytest.main([__file__])
