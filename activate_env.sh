#!/bin/bash

# 视频融合编辑器环境激活脚本
# 检查并激活conda环境，安装必要依赖

ENV_NAME="video-fusion-editor"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "=== 视频融合编辑器环境检查 ==="

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "错误: 未找到conda，请先安装Anaconda或Miniconda"
    exit 1
fi

# 检查环境是否存在
if ! conda env list | grep -q "^${ENV_NAME}"; then
    echo "环境 ${ENV_NAME} 不存在，正在创建..."
    conda create -n ${ENV_NAME} python=3.9 -y
    if [ $? -ne 0 ]; then
        echo "错误: 创建conda环境失败"
        exit 1
    fi
fi

# 激活环境
echo "激活环境: ${ENV_NAME}"
source $(conda info --base)/etc/profile.d/conda.sh
conda activate ${ENV_NAME}

if [ $? -ne 0 ]; then
    echo "错误: 激活环境失败"
    exit 1
fi

# 检查并安装依赖
echo "检查Python依赖..."
if [ -f "${SCRIPT_DIR}/requirements.txt" ]; then
    echo "安装依赖包..."
    pip install -r "${SCRIPT_DIR}/requirements.txt"
    if [ $? -ne 0 ]; then
        echo "警告: 部分依赖安装失败，请检查"
    fi
else
    echo "警告: 未找到requirements.txt文件"
fi

echo "=== 环境准备完成 ==="
echo "当前Python版本: $(python --version)"
echo "当前环境: $(conda info --envs | grep '*')"
