#!/usr/bin/env python3
"""
最终完整测试脚本
验证界面重构和递归修复后的完整功能
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.video.video_loader import VideoLoader
from src.fusion.fusion_engine import FusionEngine, FusionParams, FusionType
from src.fusion.insertion_fusion import InsertionPosition

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_complete_workflow():
    """测试完整工作流程"""
    logger.info("=== 测试完整工作流程 ===")
    
    # 测试视频路径
    video_a_path = "videos/172681-849651720_tiny.mp4"
    video_b_path = "videos/283533_medium.mp4"
    
    # 检查视频文件是否存在
    if not os.path.exists(video_a_path):
        logger.error(f"A视频文件不存在: {video_a_path}")
        return False
    
    if not os.path.exists(video_b_path):
        logger.error(f"B视频文件不存在: {video_b_path}")
        return False
    
    try:
        # 1. 创建融合引擎
        logger.info("1. 创建融合引擎...")
        fusion_engine = FusionEngine()
        
        # 2. 加载视频
        logger.info("2. 加载视频...")
        if not fusion_engine.load_video_a(video_a_path):
            logger.error("A视频加载失败")
            return False
        
        if not fusion_engine.load_video_b(video_b_path):
            logger.error("B视频加载失败")
            return False
        
        # 3. 设置融合参数
        logger.info("3. 设置融合参数...")
        fusion_params = FusionParams()
        fusion_params.fusion_type = FusionType.INSERTION
        fusion_params.alpha = 0.8
        fusion_params.resize_mode = "fit"
        
        # 设置插入位置
        positions = [
            InsertionPosition(frame_number=300, duration=1),
            InsertionPosition(frame_number=600, duration=1),
            InsertionPosition(frame_number=900, duration=1)
        ]
        fusion_params.positions = positions
        
        fusion_engine.set_fusion_params(fusion_params)
        logger.info(f"融合参数设置完成，插入位置数量: {len(positions)}")
        
        # 4. 生成预览
        logger.info("4. 生成预览...")
        preview_frames = fusion_engine.get_fusion_preview(max_frames=3)
        
        if not preview_frames:
            logger.error("预览生成失败")
            return False
        
        logger.info(f"预览生成成功，预览帧数: {len(preview_frames)}")
        
        # 5. 保存预览图像
        logger.info("5. 保存预览图像...")
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        import cv2
        for i, (frame_index, frame) in enumerate(preview_frames):
            preview_path = output_dir / f"final_complete_test_preview_{i}_frame_{frame_index}.jpg"
            cv2.imwrite(str(preview_path), frame)
            logger.info(f"预览图像已保存: {preview_path}")
        
        logger.info("✅ 完整工作流程测试通过")
        return True
        
    except Exception as e:
        logger.error(f"完整工作流程测试失败: {e}")
        return False

def test_ui_components():
    """测试UI组件"""
    logger.info("=== 测试UI组件 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.preview_window import PreviewWindow
        from src.gui.log_window import LogWindow
        from src.gui.performance_window import PerformanceWindow
        from src.gui.control_panel import ControlPanel
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 测试预览窗口
        logger.info("测试预览窗口...")
        preview_window = PreviewWindow()
        preview_window.add_info("测试预览窗口")
        preview_window.clear_preview()  # 测试清除功能
        logger.info("✅ 预览窗口测试通过")
        
        # 测试日志窗口
        logger.info("测试日志窗口...")
        log_window = LogWindow()
        log_window.add_log("INFO", "测试信息")
        log_window.add_log("WARNING", "测试警告")
        log_window.add_log("ERROR", "测试错误")
        logger.info("✅ 日志窗口测试通过")
        
        # 测试性能监控窗口
        logger.info("测试性能监控窗口...")
        performance_window = PerformanceWindow()
        logger.info("✅ 性能监控窗口测试通过")
        
        # 测试控制面板
        logger.info("测试控制面板...")
        control_panel = ControlPanel()
        
        # 测试信号发送
        signal_received = False
        def on_preview_signal():
            nonlocal signal_received
            signal_received = True
        
        control_panel.preview_requested.connect(on_preview_signal)
        control_panel.generate_preview()
        
        if signal_received:
            logger.info("✅ 控制面板信号测试通过")
        else:
            logger.error("❌ 控制面板信号测试失败")
            return False
        
        logger.info("✅ UI组件测试通过")
        return True
        
    except Exception as e:
        logger.error(f"UI组件测试失败: {e}")
        return False

def test_stability():
    """测试稳定性"""
    logger.info("=== 测试稳定性 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 测试多次预览清除操作
        logger.info("测试多次预览清除操作...")
        for i in range(10):
            main_window.clear_preview()
            main_window.on_preview_cleared()
        
        logger.info("✅ 稳定性测试通过，无递归错误")
        return True
        
    except RecursionError as e:
        logger.error(f"❌ 递归错误: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 稳定性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始最终完整测试")
    
    # 检查当前工作目录
    current_dir = os.getcwd()
    logger.info(f"当前工作目录: {current_dir}")
    
    # 测试列表
    tests = [
        ("完整工作流程", test_complete_workflow),
        ("UI组件", test_ui_components),
        ("稳定性", test_stability),
    ]
    
    # 执行测试
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"执行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    # 输出测试结果
    logger.info(f"\n{'='*50}")
    logger.info(f"测试完成: {passed}/{total} 通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！")
        logger.info("📋 功能验证总结:")
        logger.info("  ✅ 界面重构完成：独立窗口设计")
        logger.info("  ✅ 递归问题修复：应用程序稳定运行")
        logger.info("  ✅ 预览功能正常：生成和显示预览")
        logger.info("  ✅ 视频加载正常：支持多种格式")
        logger.info("  ✅ 融合引擎正常：参数设置和处理")
        logger.info("  ✅ UI组件正常：所有窗口功能完整")
        logger.info("  ✅ 应用程序就绪：可以投入使用")
        return True
    else:
        logger.error(f"⚠️  有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
