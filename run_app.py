#!/usr/bin/env python3
"""
视频融合编辑器启动脚本
Video Fusion Editor Launcher
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 7):
            print("错误: 需要Python 3.7或更高版本")
            sys.exit(1)
        
        # 检查必要的依赖
        try:
            import PyQt5
            import cv2
            import numpy as np
            import psutil
        except ImportError as e:
            print(f"错误: 缺少必要的依赖包: {e}")
            print("请运行: source activate_env.sh && pip install -r requirements.txt")
            sys.exit(1)
        
        # 导入应用程序
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.gui.main_window import MainWindow
        from src.utils.logger import Logger
        
        # 设置高DPI支持
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("视频融合编辑器")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("Augment Code")
        
        # 设置应用程序图标（如果有的话）
        # app.setWindowIcon(QIcon("resources/icon.png"))
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 显示欢迎信息
        logger = Logger.get_logger(__name__)
        logger.info("视频融合编辑器启动")
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"PyQt5版本: {PyQt5.QtCore.PYQT_VERSION_STR}")
        
        # 显示主窗口
        main_window.show()
        
        # 显示启动提示
        main_window.log_message("🎉 欢迎使用视频融合编辑器！")
        main_window.log_message("💡 提示: 可以拖拽视频文件到窗口中快速加载")
        main_window.log_message("📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
