# 视频融合编辑器 (Video Fusion Editor)

一个专业的桌面端视频编辑应用，实现A视频与B视频的多维度融合功能。

## 🌟 功能特点

### 核心功能
- **多种融合模式** - 插入融合、叠加融合、混合融合
- **实时预览** - 支持融合效果的实时预览
- **参数控制** - 完整的融合参数图形化控制
- **性能优化** - 多线程处理、智能内存管理
- **拖拽支持** - 支持拖拽视频文件快速加载

### 高级功能
- **预处理功能** - 边缘检测、直方图匹配、图像滤镜
- **文字叠加** - 支持文字内容、样式、位置、动画控制
- **参数预设** - 内置多种常用融合预设，支持自定义保存
- **性能监控** - 实时CPU、内存、处理速度监控
- **项目管理** - 项目保存、加载和管理（开发中）

## 🚀 快速开始

### 环境要求
- Python 3.7+
- macOS / Linux / Windows
- 至少4GB内存（推荐8GB+）

### 安装和运行

1. **启动应用**
   ```bash
   ./run.sh
   ```
   
   或者手动启动：
   ```bash
   source activate_env.sh
   python run_app.py
   ```

### 基本使用

1. **加载视频** - 点击工具栏按钮或拖拽视频文件
2. **设置参数** - 在融合参数标签页中调整设置
3. **预览效果** - 点击"生成预览"查看融合效果
4. **执行融合** - 点击"开始融合"处理视频
5. **导出结果** - 融合完成后导出视频文件

## 📋 主要功能

### 融合模式
- **插入融合** - 将B视频插入到A视频指定位置
- **叠加融合** - 将B视频叠加在A视频之上
- **混合融合** - 按比例混合两个视频

### 预处理功能
- **边缘检测** - Canny、Sobel、Laplacian算法
- **直方图处理** - 均衡化、匹配、对比度调整
- **图像滤镜** - 模糊、锐化、降噪等效果

### 文字叠加
- 自定义文字内容和样式
- 位置控制和动画效果
- 多种预设样式

## 🔧 技术架构

### 核心模块
- **融合引擎** - 核心算法实现
- **视频处理** - 加载、解码、编码
- **用户界面** - PyQt5图形界面
- **性能优化** - 多线程、内存管理

### 性能特性
- **多线程处理** - 并行处理提升速度
- **智能内存管理** - LRU缓存和自动清理
- **性能监控** - 实时系统状态监控
- **大文件支持** - 分块处理优化

## 📊 性能指标

- **支持分辨率** - 最高4K (3840x2160)
- **处理速度** - 1080p约30-60 FPS
- **内存使用** - 500MB-2GB
- **支持格式** - MP4, AVI, MOV, MKV, WMV

## 🛠️ 项目结构

```
video-fusion-editor/
├── src/                    # 源代码
│   ├── fusion/            # 融合算法
│   ├── video/             # 视频处理
│   ├── gui/               # 用户界面
│   └── utils/             # 工具模块
├── config.json            # 配置文件
├── output/                # 输出目录
├── videos/                # 测试视频
├── requirements.txt       # Python依赖
├── activate_env.sh        # 环境激活脚本
├── run.sh                 # 启动脚本
└── run_app.py            # 应用程序入口
```

## 📝 开发状态

### v1.0.0 (2025-06-22) - 已完成
- ✅ 完整融合算法实现
- ✅ 图形化用户界面
- ✅ 性能优化和监控
- ✅ 参数预设管理
- ✅ 拖拽文件支持
- ✅ 实时预览功能
- ✅ 主界面集成完成

### 开发中功能
- 🔄 批量处理功能
- 🔄 项目文件管理
- 🔄 更多融合算法
- 🔄 GPU加速支持

## 🔧 技术栈

- **GUI框架**: PyQt5
- **视频处理**: OpenCV, FFmpeg
- **图像处理**: PIL, NumPy, SciPy
- **科学计算**: scikit-image, matplotlib
- **性能监控**: psutil
- **测试框架**: pytest, pytest-qt

## 🐛 故障排除

### 常见问题
1. **环境激活失败** - 手动创建conda环境
2. **视频加载失败** - 检查文件格式和路径
3. **内存不足** - 降低分辨率或减少并发
4. **处理速度慢** - 启用多线程或降低质量

### 日志查看
- 界面实时日志显示
- 可导出详细日志文件
- 性能监控数据分析

## 📄 许可证

本项目采用 MIT 许可证

---

**享受视频创作的乐趣！** 🎬✨
