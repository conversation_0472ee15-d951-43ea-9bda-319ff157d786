{"tasks": [{"task_id": "config_test_task_1", "video_a_path": "/Users/<USER>/Documents/augment-projects/X/videos/243647_medium.mp4", "video_b_path": "/Users/<USER>/Documents/augment-projects/X/videos/275498_small.mp4", "output_path": "output/config_test_1.mp4", "fusion_params": {"fusion_type": "blend", "alpha": 0.5, "resize_mode": "fit", "output_fps": 30.0}, "status": "pending", "progress": 0.0, "error_message": null, "start_time": null, "end_time": null}, {"task_id": "config_test_task_2", "video_a_path": "/Users/<USER>/Documents/augment-projects/X/videos/275498_small.mp4", "video_b_path": "/Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4", "output_path": "output/config_test_2.mp4", "fusion_params": {"fusion_type": "blend", "alpha": 0.5, "resize_mode": "fit", "output_fps": 30.0}, "status": "pending", "progress": 0.0, "error_message": null, "start_time": null, "end_time": null}]}