#!/usr/bin/env python3
"""
测试界面重构后的功能
验证独立窗口和菜单集成
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_ui_structure():
    """测试UI结构"""
    logger.info("=== 测试界面重构后的UI结构 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 检查主窗口组件
        logger.info("检查主窗口组件...")
        
        # 检查控制面板是否存在
        if hasattr(main_window, 'control_panel') and main_window.control_panel:
            logger.info("✅ 控制面板存在")
        else:
            logger.error("❌ 控制面板不存在")
            return False
            
        # 检查独立窗口是否创建
        if hasattr(main_window, 'preview_window') and main_window.preview_window:
            logger.info("✅ 预览窗口已创建")
        else:
            logger.error("❌ 预览窗口未创建")
            return False
            
        if hasattr(main_window, 'log_window') and main_window.log_window:
            logger.info("✅ 日志窗口已创建")
        else:
            logger.error("❌ 日志窗口未创建")
            return False
            
        if hasattr(main_window, 'performance_window') and main_window.performance_window:
            logger.info("✅ 性能监控窗口已创建")
        else:
            logger.error("❌ 性能监控窗口未创建")
            return False
            
        # 检查菜单栏
        menubar = main_window.menuBar()
        if menubar:
            logger.info("✅ 菜单栏存在")
            
            # 检查视图菜单
            view_menu = None
            for action in menubar.actions():
                if "视图" in action.text():
                    view_menu = action.menu()
                    break
                    
            if view_menu:
                logger.info("✅ 视图菜单存在")
                
                # 检查窗口菜单项
                window_actions = []
                for action in view_menu.actions():
                    if "窗口" in action.text():
                        window_actions.append(action.text())
                        
                if window_actions:
                    logger.info(f"✅ 窗口菜单项: {window_actions}")
                else:
                    logger.warning("⚠️ 未找到窗口菜单项")
            else:
                logger.error("❌ 视图菜单不存在")
                return False
        else:
            logger.error("❌ 菜单栏不存在")
            return False
            
        logger.info("✅ UI结构测试通过")
        return True
        
    except Exception as e:
        logger.error(f"UI结构测试失败: {e}")
        return False

def test_window_functionality():
    """测试窗口功能"""
    logger.info("=== 测试独立窗口功能 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 测试日志功能
        logger.info("测试日志功能...")
        main_window.log_message("测试信息日志", "INFO")
        main_window.log_message("测试警告日志", "WARNING")
        main_window.log_message("测试错误日志", "ERROR")
        
        # 检查日志窗口是否接收到消息
        if main_window.log_window and len(main_window.log_window.log_buffer) >= 3:
            logger.info("✅ 日志功能正常")
        else:
            logger.error("❌ 日志功能异常")
            return False
            
        # 测试窗口显示功能
        logger.info("测试窗口显示功能...")
        
        # 显示预览窗口
        main_window.show_preview_window()
        if main_window.preview_window.isVisible():
            logger.info("✅ 预览窗口显示正常")
            main_window.preview_window.hide()
        else:
            logger.error("❌ 预览窗口显示异常")
            return False
            
        # 显示日志窗口
        main_window.show_log_window()
        if main_window.log_window.isVisible():
            logger.info("✅ 日志窗口显示正常")
            main_window.log_window.hide()
        else:
            logger.error("❌ 日志窗口显示异常")
            return False
            
        # 显示性能监控窗口
        main_window.show_performance_window()
        if main_window.performance_window.isVisible():
            logger.info("✅ 性能监控窗口显示正常")
            main_window.performance_window.hide()
        else:
            logger.error("❌ 性能监控窗口显示异常")
            return False
            
        logger.info("✅ 窗口功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"窗口功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始界面重构测试")
    
    # 测试列表
    tests = [
        ("UI结构测试", test_ui_structure),
        ("窗口功能测试", test_window_functionality),
    ]
    
    # 执行测试
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"执行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    # 输出测试结果
    logger.info(f"\n{'='*50}")
    logger.info(f"测试完成: {passed}/{total} 通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！界面重构成功")
        return True
    else:
        logger.error(f"⚠️  有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
