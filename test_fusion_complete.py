#!/usr/bin/env python3
"""
完整的视频融合测试脚本
测试应用程序的核心功能，包括视频加载、参数设置和融合处理
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.video.video_loader import VideoLoader
from src.fusion.fusion_engine import FusionEngine
from src.effects.edge_detector import EdgeDetector
from src.effects.histogram_matcher import HistogramMatcher
from src.effects.text_overlay import TextOverlay

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_video_loading():
    """测试视频加载功能"""
    logger.info("=== 测试视频加载功能 ===")
    
    # 测试视频路径
    video_a_path = "videos/172681-849651720_tiny.mp4"
    video_b_path = "videos/283533_medium.mp4"
    
    # 检查视频文件是否存在
    if not os.path.exists(video_a_path):
        logger.error(f"A视频文件不存在: {video_a_path}")
        return False
    
    if not os.path.exists(video_b_path):
        logger.error(f"B视频文件不存在: {video_b_path}")
        return False
    
    # 创建视频加载器
    loader_a = VideoLoader()
    loader_b = VideoLoader()
    
    # 加载视频A
    logger.info(f"加载A视频: {video_a_path}")
    if not loader_a.load_video(video_a_path):
        logger.error("A视频加载失败")
        return False
    
    info_a = loader_a.get_current_info()
    logger.info(f"A视频信息: {info_a}")
    
    # 加载视频B
    logger.info(f"加载B视频: {video_b_path}")
    if not loader_b.load_video(video_b_path):
        logger.error("B视频加载失败")
        return False
    
    info_b = loader_b.get_current_info()
    logger.info(f"B视频信息: {info_b}")
    
    logger.info("✅ 视频加载测试通过")
    return True

def test_fusion_engine():
    """测试融合引擎功能"""
    logger.info("=== 测试融合引擎功能 ===")
    
    # 创建融合引擎
    fusion_engine = FusionEngine()
    
    # 加载视频
    video_a_path = "videos/172681-849651720_tiny.mp4"
    video_b_path = "videos/283533_medium.mp4"
    
    if not fusion_engine.load_video_a(video_a_path):
        logger.error("融合引擎加载A视频失败")
        return False
    
    if not fusion_engine.load_video_b(video_b_path):
        logger.error("融合引擎加载B视频失败")
        return False
    
    # 设置融合参数
    from src.fusion.fusion_engine import FusionParams
    fusion_params = FusionParams(
        fusion_type='insertion',
        insertion_mode='direct',
        resize_mode='适应',
        alpha=0.7,
        positions=[(100, 100)],  # 在(100,100)位置插入
        output_fps=30.0,
        output_codec='mp4v'
    )

    fusion_engine.set_fusion_params(fusion_params)
    logger.info(f"融合参数设置完成: {fusion_params}")
    
    logger.info("✅ 融合引擎测试通过")
    return True

def test_preprocessing():
    """测试预处理功能"""
    logger.info("=== 测试预处理功能 ===")
    
    # 创建视频加载器
    loader = VideoLoader()
    video_path = "videos/172681-849651720_tiny.mp4"
    
    if not loader.load_video(video_path):
        logger.error("预处理测试：视频加载失败")
        return False
    
    # 获取第一帧
    frame = loader.get_frame(0)
    if frame is None:
        logger.error("预处理测试：无法获取视频帧")
        return False
    
    # 测试边缘检测
    from src.effects.edge_detector import EdgeDetectionMethod
    edge_detector = EdgeDetector()
    edge_frame = edge_detector.detect_edges(frame, method=EdgeDetectionMethod.CANNY)
    logger.info("边缘检测处理完成")

    # 测试直方图匹配
    hist_matcher = HistogramMatcher()
    matched_frame = hist_matcher.match_histograms(frame, frame)  # 自匹配测试
    logger.info("直方图匹配处理完成")

    # 测试文字叠加
    text_overlay = TextOverlay()
    text_frame = text_overlay.add_text(frame, "测试文字", (50, 50))
    logger.info("文字叠加处理完成")
    
    logger.info("✅ 预处理功能测试通过")
    return True

def test_fusion_processing():
    """测试完整的融合处理"""
    logger.info("=== 测试完整融合处理 ===")
    
    # 创建融合引擎
    fusion_engine = FusionEngine()
    
    # 加载视频
    video_a_path = "videos/172681-849651720_tiny.mp4"
    video_b_path = "videos/283533_medium.mp4"
    
    if not fusion_engine.load_video_a(video_a_path):
        logger.error("融合处理测试：A视频加载失败")
        return False
    
    if not fusion_engine.load_video_b(video_b_path):
        logger.error("融合处理测试：B视频加载失败")
        return False
    
    # 设置融合参数
    from src.fusion.fusion_engine import FusionParams
    fusion_params = FusionParams(
        fusion_type='insertion',
        insertion_mode='direct',
        resize_mode='适应',
        alpha=0.8,
        positions=[(200, 150)],
        output_fps=30.0,
        output_codec='mp4v'
    )

    fusion_engine.set_fusion_params(fusion_params)
    
    # 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 设置输出路径
    output_path = output_dir / "test_fusion_result.mp4"
    
    # 执行融合处理（只处理前10帧进行快速测试）
    logger.info("开始融合处理...")
    try:
        success = fusion_engine.process_fusion(
            str(output_path),
            max_frames=10  # 限制处理帧数以加快测试
        )
        
        if success:
            logger.info(f"✅ 融合处理成功，输出文件: {output_path}")
            
            # 检查输出文件是否存在
            if output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"输出文件大小: {file_size / 1024:.2f} KB")
                return True
            else:
                logger.error("输出文件不存在")
                return False
        else:
            logger.error("融合处理失败")
            return False
            
    except Exception as e:
        logger.error(f"融合处理异常: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始视频融合编辑器完整测试")
    
    # 检查当前工作目录
    current_dir = os.getcwd()
    logger.info(f"当前工作目录: {current_dir}")
    
    # 测试列表
    tests = [
        ("视频加载功能", test_video_loading),
        ("融合引擎功能", test_fusion_engine),
        ("预处理功能", test_preprocessing),
        ("完整融合处理", test_fusion_processing),
    ]
    
    # 执行测试
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"执行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    # 输出测试结果
    logger.info(f"\n{'='*50}")
    logger.info(f"测试完成: {passed}/{total} 通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！视频融合编辑器功能正常")
        return True
    else:
        logger.error(f"⚠️  有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
