#!/usr/bin/env python3
"""
测试递归问题修复
验证预览清除功能不再有递归错误
"""

import sys
import os
import logging

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_preview_window_clear():
    """测试预览窗口清除功能"""
    logger.info("=== 测试预览窗口清除功能 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.preview_window import PreviewWindow
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建预览窗口
        preview_window = PreviewWindow()
        
        # 测试清除预览功能
        logger.info("测试清除预览...")
        preview_window.clear_preview()
        logger.info("✅ 预览清除成功，无递归错误")
        
        # 测试多次清除
        for i in range(5):
            preview_window.clear_preview()
            logger.info(f"✅ 第{i+1}次清除成功")
        
        return True
        
    except RecursionError as e:
        logger.error(f"❌ 递归错误: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 其他错误: {e}")
        return False

def test_control_panel_signals():
    """测试控制面板信号"""
    logger.info("=== 测试控制面板信号 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.control_panel import ControlPanel
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建控制面板
        control_panel = ControlPanel()
        
        # 测试信号连接
        signal_count = 0
        
        def on_preview_requested():
            nonlocal signal_count
            signal_count += 1
            logger.info(f"收到预览请求信号，计数: {signal_count}")
        
        # 连接信号
        control_panel.preview_requested.connect(on_preview_requested)
        
        # 测试生成预览
        logger.info("测试生成预览信号...")
        control_panel.generate_preview()
        
        if signal_count == 1:
            logger.info("✅ 预览信号发送正常，无重复")
            return True
        else:
            logger.error(f"❌ 预览信号异常，计数: {signal_count}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 控制面板测试失败: {e}")
        return False

def test_main_window_integration():
    """测试主窗口集成"""
    logger.info("=== 测试主窗口集成 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 测试预览清除
        logger.info("测试主窗口预览清除...")
        main_window.clear_preview()
        logger.info("✅ 主窗口预览清除成功")
        
        # 测试预览清除回调
        logger.info("测试预览清除回调...")
        main_window.on_preview_cleared()
        logger.info("✅ 预览清除回调成功")
        
        return True
        
    except RecursionError as e:
        logger.error(f"❌ 主窗口递归错误: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 主窗口测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始递归问题修复测试")
    
    # 测试列表
    tests = [
        ("预览窗口清除功能", test_preview_window_clear),
        ("控制面板信号", test_control_panel_signals),
        ("主窗口集成", test_main_window_integration),
    ]
    
    # 执行测试
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"执行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    # 输出测试结果
    logger.info(f"\n{'='*50}")
    logger.info(f"测试完成: {passed}/{total} 通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！递归问题已修复")
        return True
    else:
        logger.error(f"⚠️  有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
